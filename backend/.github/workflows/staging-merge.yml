name: On staging merge

on:
  push:
    branches: [ "staging" ]

permissions:
  id-token: write # This is required for requesting the JWT
  contents: read # This is required for actions/checkout

jobs:
  configuration:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: ${{ vars.AWS_OICD_ROLE_ARN_STAGING }}
          aws-region: ${{ vars.AWS_REGION }}
          mask-aws-account-id: false

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v2

      - name: Set vars
        id: vars
        env:
          ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
        run: |
          git_commit="${GITHUB_SHA:0:7}";
          service_name="${GITHUB_REPOSITORY##*/}";
          ecr_repo_name="${ECR_REGISTRY}/${service_name}";

          echo "git_commit=$git_commit" >> $GITHUB_OUTPUT;
          echo "ecr_repo_name=${ecr_repo_name}" >> $GITHUB_OUTPUT;
          echo "ecr_sha_image=${ecr_repo_name}:${git_commit}" >> $GITHUB_OUTPUT;
          echo "service_name=$service_name" >> $GITHUB_OUTPUT;

    outputs:
      git_commit: ${{ steps.vars.outputs.git_commit }}
      ecr_repo_name: ${{ steps.vars.outputs.ecr_repo_name }}
      ecr_sha_image: ${{ steps.vars.outputs.ecr_sha_image }}
      service_name: ${{ steps.vars.outputs.service_name }}

  validation:
    runs-on: ubuntu-latest
    services:
      postgres:
        image: postgres:16-alpine
        env:
          POSTGRES_USER: test-user
          POSTGRES_PASSWORD: password
          POSTGRES_DB: test_db
        # Set health checks to wait until postgres has started
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          # Maps tcp port 5432 on service container to the host
          - 5432:5432
    steps:
      - uses: actions/checkout@v4
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
      - name: Build the test Docker image
        uses: docker/build-push-action@v6
        with:
          context: .
          pull: true
          outputs: type=docker
          tags: backend-test
          target: test
          cache-from: type=gha,scope=amd
          cache-to: type=gha,mode=max,scope=amd
      #        run: docker build  --target test . --file Dockerfile --tag backend-test
      - name: Run the tests and validation
        run: docker run --network="host" --rm --env DATABASE_URL="test-user:password@localhost:5432/test_db" backend-test
  build-push-release:
    runs-on: ubuntu-latest
    needs: [configuration, validation]
    permissions:
      contents: write
      issues: write
      pull-requests: write
      id-token: write
    steps:
      - name: Generate token
        id: app-token
        uses: actions/create-github-app-token@v1
        with:
          app-id: ${{ secrets.SEMANTIC_APP_ID }}
          private-key: ${{ secrets.SEMANTIC_APP_KEY }}

      - uses: actions/checkout@v4

      - name: Setup Node
        uses: actions/setup-node@v3
        with:
          node-version: "20.x"

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: ${{ vars.AWS_OICD_ROLE_ARN_STAGING }}
          aws-region: ${{ vars.AWS_REGION }}
          mask-aws-account-id: false

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v2

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Build and push the Docker image
        uses: docker/build-push-action@v6
        with:
          context: .
          pull: true
          push: true
          tags: "${{ needs.configuration.outputs.ecr_repo_name }}:${{ needs.configuration.outputs.git_commit }}"
          target: runtime
          cache-from: type=gha,scope=amd
          cache-to: type=gha,mode=max,scope=amd

      - name: Tag and push version and latest images
        env:
          CURRENT_IMAGE: "${{ needs.configuration.outputs.ecr_repo_name }}:${{ needs.configuration.outputs.git_commit }}"
          ECR_REPO_NAME: ${{ needs.configuration.outputs.ecr_repo_name }}
        run: |
          docker buildx imagetools create "${CURRENT_IMAGE}" --tag "${ECR_REPO_NAME}:staging"


      - name: Redeploy the ECS service
        run: |
          aws ecs update-service --cluster ${{ secrets.SERVICES_ECS_CLUSTER_NAME_STAGING }} --service ${{ secrets.BACKEND_ECS_SERVICE_NAME_STAGING }} --force-new-deployment

    outputs:
      image: "${{ needs.configuration.outputs.ecr_repo_name }}:${{ needs.configuration.outputs.git_commit}}"