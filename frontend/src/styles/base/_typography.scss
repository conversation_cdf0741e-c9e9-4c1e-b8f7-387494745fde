@use '../abstracts/tokens';
@use '../abstracts/mixins';

@font-face {
  font-family: 'Ryker';
  src:
    url('/fonts/ryker/rykertext-bold.woff2') format('woff2'),
    url('/fonts/ryker/rykertext-bold.woff') format('woff'),
    url('/fonts/ryker/rykertext-bold.otf') format('opentype');
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Ryker';
  src:
    url('/fonts/ryker/rykertext-black.woff2') format('woff2'),
    url('/fonts/ryker/rykertext-black.woff') format('woff'),
    url('/fonts/ryker/rykertext-black.otf') format('opentype');
  font-weight: 900;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Quasimoda';
  src:
    url('/fonts/quasimoda/quasimoda-regular.woff2') format('woff2'),
    url('/fonts/quasimoda/quasimoda-regular.woff') format('woff'),
    url('/fonts/quasimoda/quasimoda-regular.otf') format('opentype');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Quasimoda';
  src:
    url('/fonts/quasimoda/quasimoda-bold.woff2') format('woff2'),
    url('/fonts/quasimoda/quasimoda-bold.woff') format('woff'),
    url('/fonts/quasimoda/quasimoda-bold.otf') format('opentype');
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}

body {
  @include mixins.body;
}

h1 {
  @include mixins.h1;
}

h2 {
  @include mixins.h2;
}

h3 {
  @include mixins.h3;
}

h4 {
  @include mixins.h4;
}

h5 {
  @include mixins.h5;
}

h6 {
  @include mixins.h6;
}

.body-l {
  @include mixins.body-l;
}

.body {
  @include mixins.body;
}

.body-s {
  @include mixins.body-s;
}

// Bold variants
.body-l-bold {
  @include mixins.body-l;
  font-weight: 700;
}

.body-bold {
  @include mixins.body;
  font-weight: 700;
}

.body-s-bold {
  @include mixins.body-s;
  font-weight: 700;
}
