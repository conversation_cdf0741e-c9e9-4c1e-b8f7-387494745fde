export type FileStatus = 'uploading' | 'success' | 'error';
export type FileType = 'image' | 'document' | 'pdf';

export interface BaseFile {
  id: string;
  name: string;
  type: string;
  size?: number;
  file?: File;
  status: FileStatus;
  progress?: number;
  createdAt?: string;
}

export interface UploadedFile extends BaseFile {
  documentId: number;
  url?: string;
  cdnUrl?: string;
  fileExtension?: string;
  originalFileName?: string;
  sizeInKiloBytes?: number;
  fileId?: string;
}

export interface CategorizedFile extends UploadedFile {
  category: string;
  label: string;
}

export interface LocalFile extends BaseFile {
  url?: string;
}

export type UniversalFile = CategorizedFile | UploadedFile | LocalFile;

export const isUploadedFile = (file: UniversalFile): file is UploadedFile => {
  return 'documentId' in file && typeof file.documentId === 'number';
};

export const isCategorizedFile = (file: UniversalFile): file is CategorizedFile => {
  return 'category' in file;
};

export const isLocalFile = (file: UniversalFile): file is LocalFile => {
  return !('documentId' in file);
};
