'use client';

import { SignedIn, SignedOut, SignInButton, SignUpButton } from '@clerk/nextjs';
import { Button } from '@/components/Button/Button';
import { ButtonType } from '@/components/Button/Button.types';
import { FirstPromptPage } from '@/components/FirstPromptPage';
import React from 'react';

export default function Home() {
  return (
    <>
      <SignedOut>
        <div className="min-h-[calc(100vh-64px)] flex flex-col items-center justify-center p-8 text-center">
          <main className="max-w-2xl">
            <h1 className="text-3xl font-bold mb-4">
              Hey, I&apos;m Alfie. Your AI-powered home assistant.
            </h1>
            <p className="mb-8">
              I take home admin off your plate to book trusted tradespeople, keep on top of your
              to-dos, and tackle the stuff no one wants to deal with.
            </p>
            <p className="mb-8">Sign up for a calmer, smarter home.</p>

            <div className="space-y-4">
              <div className="flex gap-4 justify-center">
                <SignUpButton mode="modal">
                  <Button>Sign up</Button>
                </SignUpButton>
                <SignInButton mode="modal">
                  <Button type={ButtonType.SECONDARY}>Log in</Button>
                </SignInButton>
              </div>
            </div>
          </main>
        </div>
      </SignedOut>

      <SignedIn>
        <FirstPromptPage />
      </SignedIn>
    </>
  );
}
