import { Alert } from '@/components/Alert';
import { AlertColor } from '@/components/Alert/Alert.types';
import { AlfieNotification, NotificationSeverity } from '@/hooks/useNotifications';
import React from 'react';

type Props = {
  notification: AlfieNotification;
  onClose: (id: string) => void;
};

function getColor(severity: NotificationSeverity) {
  switch (severity) {
    case 'error':
      return AlertColor.DANGER;
    case 'warning':
      return AlertColor.WARNING;
    case 'info':
      return AlertColor.NOTICE;
    default:
      throw new Error(`Unhandled severity: ${severity}`);
  }
}

export function NotificationAlert({ notification, onClose }: Props) {
  return (
    <Alert
      color={getColor(notification.severity)}
      headingText={notification.title}
      bodyText={notification.message}
      showButton={false}
      onClose={() => onClose(notification.id)}
    />
  );
}