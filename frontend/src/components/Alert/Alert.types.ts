import { ComponentType } from 'react';

export enum AlertColor {
  DEFAULT = 'default',
  INFO = 'info',
  SUCCESS = 'success',
  WARNING = 'warning',
  DANGER = 'danger',
}

export interface AlertProps {
  headingText?: string;
  bodyText?: string;
  showButton?: boolean;
  showBodyText?: boolean;
  showRightIcon?: boolean;
  showLeftIcon?: boolean;
  showAlertHeading?: boolean;
  color?: AlertColor;
  className?: string;
  button?: React.ReactNode;
  leftIcon?: ComponentType<{ className?: string; color?: string }>;
  inline?: boolean;
  onClose?: () => void;
}
