.alert {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: var(--spacing-3);
  padding: var(--spacing-4);
  border-radius: var(--border-radius-rounded-md);
  position: relative;
  width: 100%;

  &.inline {
    display: flex;
    flex-direction: row;
    align-items: center;
    border-radius: var(--border-radius-rounded-lg);
    padding: var(--spacing-2);
    min-width: 0;
    gap: var(--spacing-1-5);

    @media (max-width: 768px) {
      gap: var(--spacing-1);
    }

    .inlineContent {
      display: flex;
      gap: var(--spacing-1-5);
      align-items: flex-start;
      width: 100%;
    }

    &.default {
      background: var(--colors-orange-50);
      color: var(--colors-orange-800);
    }

    &.info {
      background: var(--colors-blue-50);
      color: var(--colors-blue-800);
    }

    &.success {
      background: var(--colors-green-50);
      color: var(--colors-green-800);
    }

    &.warning {
      background: var(--colors-orange-50);
      color: var(--colors-orange-800);
    }

    &.danger {
      background: var(--colors-red-50);
      color: var(--colors-red-800);
    }

    .icon {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 17px;
      height: 17px;
      margin-top: 3px;
    }

    .heading {
      font-family: Quasimoda, Regular;
      font-size: 14px;
      line-height: auto;
      flex: 1;
      font-weight: 400;
      padding-right: 0;
    }
  }

  .closeButton {
    position: absolute;
    top: 14px;
    right: var(--spacing-2);
  }

  &.default {
    background-color: var(--colors-gray-50);
  }

  &.info {
    background-color: var(--colors-blue-50);
  }

  &.success {
    background-color: var(--colors-green-50);
  }

  &.warning {
    background-color: var(--colors-orange-50);
  }

  &.danger {
    background-color: var(--colors-red-50);
  }
}

.header {
  display: flex;
  width: 100%;
  align-items: center;
  gap: var(--spacing-2);
  flex: 0 0 auto;
}

.icon {
  position: relative;
  width: var(--spacing-6);
  height: var(--spacing-6);
  flex-shrink: 0;

  &.default {
    color: var(--colors-gray-800);
  }

  &.info {
    color: var(--colors-blue-800);
  }

  &.success {
    color: var(--colors-green-800);
  }

  &.warning {
    color: var(--colors-orange-800);
  }

  &.danger {
    color: var(--colors-red-800);
  }
}

.heading {
  font-family: Quasimoda;
  font-size: 16px;
  font-weight: 700;
  line-height: 24px;
  text-align: left;
  text-underline-position: from-font;
  text-decoration-skip-ink: none;
  flex: 1;
  padding-right: var(--spacing-6);

  &.default {
    color: var(--colors-gray-800);
  }

  &.info {
    color: var(--colors-blue-800);
  }

  &.success {
    color: var(--colors-green-800);
  }

  &.warning {
    color: var(--colors-orange-800);
  }

  &.danger {
    color: var(--colors-red-800);
  }
}

.body {
  font-family: Quasimoda;
  font-size: 14px;
  font-weight: 400;
  line-height: 21px;
  text-align: left;
  text-underline-position: from-font;
  text-decoration-skip-ink: none;
  position: relative;
  width: 100%;

  &.default {
    color: var(--colors-gray-800);
  }

  &.info {
    color: var(--colors-blue-800);
  }

  &.success {
    color: var(--colors-green-800);
  }

  &.warning {
    color: var(--colors-orange-800);
  }

  &.danger {
    color: var(--colors-red-800);
  }
}

.content {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: var(--spacing-1-5);
  flex: 0 0 auto;
  width: 100%;
}
