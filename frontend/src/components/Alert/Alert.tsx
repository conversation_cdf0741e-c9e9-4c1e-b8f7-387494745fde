import React from 'react';
import classNames from 'classnames';
import * as HugeIcons from '@hugeicons/react';
import { Button } from '../Button';
import styles from './Alert.module.scss';
import { AlertColor, AlertProps } from './Alert.types';
import { ButtonColor, ButtonSize, ButtonType } from '../Button/Button.types';

export const Alert = ({
  headingText = 'Alert heading',
  showButton = true,
  showBodyText = true,
  bodyText = 'Aww yeah, you successfully read this important alert message. This example text is going to run a bit longer so that you can see how spacing within an alert works with this kind of content.',
  showRightIcon = true,
  showLeftIcon = true,
  showAlertHeading = true,
  color = AlertColor.DEFAULT,
  className,
  button,
  leftIcon,
  inline = false,
  onClose,
}: AlertProps): JSX.Element => {
  const getIconColor = () => {
    switch (color) {
      case AlertColor.DANGER:
        return 'var(--colors-red-800)';
      case AlertColor.INFO:
        return 'var(--colors-blue-800)';
      case AlertColor.SUCCESS:
        return 'var(--colors-green-800)';
      case AlertColor.WARNING:
        return 'var(--colors-orange-800)';
      default:
        return 'var(--colors-gray-800)';
    }
  };

  const getCloseButtonColor = () => {
    switch (color) {
      case AlertColor.DANGER:
        return ButtonColor.RED;
      case AlertColor.INFO:
        return ButtonColor.BLUE_SECONDARY;
      case AlertColor.SUCCESS:
        return ButtonColor.GREEN_PRIMARY;
      case AlertColor.WARNING:
        return ButtonColor.ORANGE;
      default:
        return ButtonColor.BLUE_SECONDARY;
    }
  };

  const DefaultLeftIcon =
    color === AlertColor.SUCCESS
      ? HugeIcons.CheckmarkCircle01Icon
      : HugeIcons.InformationCircleIcon;

  const LeftIcon = leftIcon || DefaultLeftIcon;

  const defaultButton = inline ? (
    <Button type={ButtonType.PRIMARY} color={getCloseButtonColor()} size={ButtonSize.XS}>
      {button ? undefined : 'Retry'}
    </Button>
  ) : (
    <Button
      className="!flex-[0_0_auto]"
      color={getCloseButtonColor()}
      leftIconName="ViewIcon"
      showLeftIcon={true}
      size={ButtonSize.XS}
    >
      {button ? undefined : 'View more'}
    </Button>
  );

  if (inline) {
    return (
      <div className={classNames(styles.alert, styles[color], styles.inline, className)}>
        <div className={styles.inlineContent}>
          {showLeftIcon && (
            <LeftIcon
              className={classNames(styles.icon, styles[color])}
              color={getIconColor()}
              width={17}
              height={17}
            />
          )}
          {showAlertHeading && (
            <div className={classNames(styles.heading, styles[color])}>{headingText}</div>
          )}
        </div>
        {showButton && (button || defaultButton)}
      </div>
    );
  }

  return (
    <div className={classNames(styles.alert, styles[color], className)}>
      <div className={styles.content}>
        <div className={styles.header}>
          {showLeftIcon && (
            <LeftIcon className={classNames(styles.icon, styles[color])} color={getIconColor()} />
          )}

          {showAlertHeading && (
            <div className={classNames(styles.heading, styles[color])}>{headingText}</div>
          )}

          {showRightIcon && !inline && (
            <Button
              color={getCloseButtonColor()}
              iconOnly
              size={ButtonSize.SM}
              type={ButtonType.TERTIARY}
              leftIconName="Cancel01Icon"
              className={styles.closeButton}
              onClick={onClose}
            />
          )}
        </div>

        {showBodyText && !inline && (
          <p className={classNames(styles.body, styles[color])}>{bodyText}</p>
        )}
      </div>

      {showButton && (button || defaultButton)}
    </div>
  );
};
