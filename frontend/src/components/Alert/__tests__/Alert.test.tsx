import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import { vi } from "vitest";
import { Alert } from "../Alert";
import { AlertColor } from "../Alert.types";

// Mock HugeIcons
vi.mock("@hugeicons/react", () => ({
  CheckmarkCircle01Icon: ({ className }: { className: string }) => (
    <div data-testid="mock-icon" className={className}>
      Mock Success Icon
    </div>
  ),
  InformationCircleIcon: ({ className }: { className: string }) => (
    <div data-testid="mock-icon" className={className}>
      Mock Info Icon
    </div>
  ),
  AlertCircleIcon: ({ className }: { className: string }) => (
    <div data-testid="mock-icon" className={className}>
      Mock Warning Icon
    </div>
  ),
  AlertTriangleIcon: ({ className }: { className: string }) => (
    <div data-testid="mock-icon" className={className}>
      Mock Error Icon
    </div>
  ),
  ViewIcon: ({ className }: { className: string }) => (
    <div data-testid="mock-icon" className={className}>
      Mock View Icon
    </div>
  ),
  Cancel01Icon: ({ className }: { className: string }) => (
    <div data-testid="mock-icon" className={className}>
      Mock Cancel Icon
    </div>
  ),
}));

describe("Alert Component", () => {
  const defaultProps = {
    headingText: "Test Alert",
    bodyText: "This is a test alert message",
    color: AlertColor.DEFAULT,
    showButton: true,
    showBodyText: true,
    showRightIcon: true,
    showLeftIcon: true,
    showAlertHeading: true,
  };

  // Basic Rendering Tests
  describe("Rendering", () => {
    it("renders alert with heading and body text correctly", () => {
      render(<Alert {...defaultProps} />);
      expect(screen.getByText("Test Alert")).toBeInTheDocument();
      expect(
        screen.getByText("This is a test alert message")
      ).toBeInTheDocument();
    });

    it("renders without heading when showAlertHeading is false", () => {
      render(<Alert {...defaultProps} showAlertHeading={false} />);
      expect(screen.queryByText("Test Alert")).not.toBeInTheDocument();
    });

    it("renders without body text when showBodyText is false", () => {
      render(<Alert {...defaultProps} showBodyText={false} />);
      expect(
        screen.queryByText("This is a test alert message")
      ).not.toBeInTheDocument();
    });

    it("renders without button when showButton is false", () => {
      render(<Alert {...defaultProps} showButton={false} />);
      expect(screen.queryByText("View more")).not.toBeInTheDocument();
    });
  });

  // Icon Tests
  describe("Icons", () => {
    it("renders left icon when showLeftIcon is true", () => {
      render(<Alert {...defaultProps} showLeftIcon={true} />);
      const icons = screen.getAllByTestId("mock-icon");
      const leftIcon = icons.find(
        (icon) => icon.textContent === "Mock Info Icon"
      );
      expect(leftIcon).toBeInTheDocument();
      expect(leftIcon).toHaveTextContent("Mock Info Icon");
    });

    it("renders right icon when showRightIcon is true", () => {
      render(<Alert {...defaultProps} showRightIcon={true} />);
      const closeButton = screen.getByRole("button", { name: /view more/i });
      expect(closeButton).toBeInTheDocument();
    });

    it("renders custom left icon when provided", () => {
      const CustomIcon = ({ className }: { className: string }) => (
        <div data-testid="custom-icon" className={className}>
          Custom Icon
        </div>
      );
      render(<Alert {...defaultProps} leftIcon={CustomIcon} />);
      expect(screen.getByTestId("custom-icon")).toBeInTheDocument();
    });

    it("does not render icons when show flags are false", () => {
      render(
        <Alert
          {...defaultProps}
          showLeftIcon={false}
          showRightIcon={false}
          showButton={false} // Also hide the view button to avoid its icon
        />
      );
      expect(screen.queryByTestId("mock-icon")).not.toBeInTheDocument();
      const closeButton = screen.queryByRole("button", { name: /close/i });
      expect(closeButton).not.toBeInTheDocument();
    });
  });

  // Color Variant Tests
  describe("Color Variants", () => {
    it.each([
      [AlertColor.DEFAULT, "_default_fb2b94"],
      [AlertColor.INFO, "_info_fb2b94"],
      [AlertColor.SUCCESS, "_success_fb2b94"],
      [AlertColor.WARNING, "_warning_fb2b94"],
      [AlertColor.DANGER, "_danger_fb2b94"],
    ])("renders %s color variant correctly", (color, expectedClass) => {
      const { container } = render(<Alert {...defaultProps} color={color} />);
      const alert = container.querySelector("._alert_fb2b94");
      expect(alert).toHaveClass(expectedClass);
    });
  });

  // Button Tests
  describe("Button", () => {
    it("renders default button correctly", () => {
      render(<Alert {...defaultProps} />);
      expect(screen.getByText("View more")).toBeInTheDocument();
    });

    it("renders custom button when provided", () => {
      const customButton = <button>Custom Button</button>;
      render(<Alert {...defaultProps} button={customButton} />);
      expect(screen.getByText("Custom Button")).toBeInTheDocument();
    });

    it("handles button click", () => {
      const handleClick = vi.fn();
      render(
        <Alert
          {...defaultProps}
          button={<button onClick={handleClick}>Click Me</button>}
        />
      );
      fireEvent.click(screen.getByText("Click Me"));
      expect(handleClick).toHaveBeenCalledTimes(1);
    });
  });

  // Class Composition Tests
  describe("Class Composition", () => {
    it("applies custom className correctly", () => {
      const customClass = "custom-class";
      const { container } = render(
        <Alert {...defaultProps} className={customClass} />
      );
      const alert = container.querySelector("._alert_fb2b94");
      expect(alert).toHaveClass(customClass);
    });

    it("combines multiple variant classes correctly", () => {
      const { container } = render(
        <Alert
          {...defaultProps}
          color={AlertColor.SUCCESS}
          className="custom-class"
        />
      );
      const alert = container.querySelector("._alert_fb2b94");
      expect(alert).toHaveClass("_success_fb2b94", "custom-class");
    });
  });

  // Edge Cases
  describe("Edge Cases", () => {
    it("handles empty texts gracefully", () => {
      const { container } = render(
        <Alert {...defaultProps} headingText="" bodyText="" />
      );
      const alert = container.querySelector("._alert_fb2b94");
      expect(alert).toBeInTheDocument();
    });

    it("handles missing optional props", () => {
      const { container } = render(<Alert bodyText="Required body text" />);
      const alert = container.querySelector("._alert_fb2b94");
      expect(alert).toBeInTheDocument();
    });
  });
});
