import React from "react";
import { render } from "@testing-library/react";
import { Alert } from "../Alert";
import { AlertColor } from "../Alert.types";

describe("Alert Snapshots", () => {
  const defaultProps = {
    headingText: "Test Alert",
    bodyText: "This is a test alert message",
    color: AlertColor.DEFAULT,
  };

  it("matches default alert snapshot", () => {
    const { container } = render(<Alert {...defaultProps} />);
    expect(container).toMatchSnapshot();
  });

  it("matches info alert snapshot", () => {
    const { container } = render(
      <Alert {...defaultProps} color={AlertColor.INFO} />
    );
    expect(container).toMatchSnapshot();
  });

  it("matches success alert snapshot", () => {
    const { container } = render(
      <Alert {...defaultProps} color={AlertColor.SUCCESS} />
    );
    expect(container).toMatchSnapshot();
  });

  it("matches warning alert snapshot", () => {
    const { container } = render(
      <Alert {...defaultProps} color={AlertColor.WARNING} />
    );
    expect(container).toMatchSnapshot();
  });

  it("matches danger alert snapshot", () => {
    const { container } = render(
      <Alert {...defaultProps} color={AlertColor.DANGER} />
    );
    expect(container).toMatchSnapshot();
  });

  it("matches alert without icons snapshot", () => {
    const { container } = render(
      <Alert {...defaultProps} showLeftIcon={false} showRightIcon={false} />
    );
    expect(container).toMatchSnapshot();
  });

  it("matches alert with custom button snapshot", () => {
    const { container } = render(
      <Alert {...defaultProps} button={<button>Custom Button</button>} />
    );
    expect(container).toMatchSnapshot();
  });

  it("matches inline desktop default alert snapshot", () => {
    const { container } = render(
      <Alert {...defaultProps} inline />
    );
    expect(container).toMatchSnapshot();
  });

  it("matches inline mobile default alert snapshot", () => {
    const { container } = render(
      <Alert {...defaultProps} inline />
    );
    expect(container).toMatchSnapshot();
  });

  it("matches inline desktop info alert snapshot", () => {
    const { container } = render(
      <Alert {...defaultProps} color={AlertColor.INFO} inline />
    );
    expect(container).toMatchSnapshot();
  });

  it("matches inline mobile info alert snapshot", () => {
    const { container } = render(
      <Alert {...defaultProps} color={AlertColor.INFO} inline />
    );
    expect(container).toMatchSnapshot();
  });

  it("matches inline desktop success alert snapshot", () => {
    const { container } = render(
      <Alert {...defaultProps} color={AlertColor.SUCCESS} inline />
    );
    expect(container).toMatchSnapshot();
  });

  it("matches inline mobile success alert snapshot", () => {
    const { container } = render(
      <Alert {...defaultProps} color={AlertColor.SUCCESS} inline />
    );
    expect(container).toMatchSnapshot();
  });

  it("matches inline desktop warning alert snapshot", () => {
    const { container } = render(
      <Alert {...defaultProps} color={AlertColor.WARNING} inline />
    );
    expect(container).toMatchSnapshot();
  });

  it("matches inline mobile warning alert snapshot", () => {
    const { container } = render(
      <Alert {...defaultProps} color={AlertColor.WARNING} inline />
    );
    expect(container).toMatchSnapshot();
  });

  it("matches inline desktop danger alert snapshot", () => {
    const { container } = render(
      <Alert {...defaultProps} color={AlertColor.DANGER} inline />
    );
    expect(container).toMatchSnapshot();
  });

  it("matches inline mobile danger alert snapshot", () => {
    const { container } = render(
      <Alert {...defaultProps} color={AlertColor.DANGER} inline />
    );
    expect(container).toMatchSnapshot();
  });
});
