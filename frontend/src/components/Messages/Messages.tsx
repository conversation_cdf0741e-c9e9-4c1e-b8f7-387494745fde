'use client';

import React, { useCallback, useEffect, useMemo, useRef } from 'react';
import { Message } from '@/components/Message';
import styles from './Messages.module.scss';
import { MessagesProps } from './Messages.types';
import { MessageTypeValue } from '@/types/messages';
import { MultiFieldInput } from '@/components/MultiFieldInput/MultiFieldInput';
import { MultiFieldInputType } from '@/components/MultiFieldInput/MultiFieldInput.types';
import { useAuth, useUser } from '@clerk/nextjs';
import { debounce } from 'lodash';
import { AddressFields as WidgetAddressFields, useWidgets } from '@/hooks/useWidgets';
import { useMessages } from '@/hooks/useMessages';
import { fetchJob } from '@/api/jobs';
import { useBreakpoint } from '@/utils/breakpointUtils';
import { Alert } from '../Alert/Alert';
import { AlertColor } from '../Alert/Alert.types';
import { Button } from '../Button';
import { ButtonColor, ButtonSize, ButtonType } from '../Button/Button.types';
import { useChats } from '@/hooks/useChats';
import useChatParams from '@/hooks/useChatParams';
import useMessageSender from '@/hooks/useMessageSender';

export const Messages: React.FC<MessagesProps> = ({
  messages,
  isLoading,
  hasMore,
  isSubmitting,
  onLoadMore,
  anchorRef,
  optimisticMessage,
}): JSX.Element => {
  const { jobSummaryConfirmed, setJobSummaryConfirmed, messageHeight } = useMessages();
  const sentinelRef = useRef(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const prevMessagesLengthRef = useRef(messages.length);
  const { getToken } = useAuth();
  const { chatId } = useChatParams();
  const { isLoaded, user } = useUser();
  const { isDesktop } = useBreakpoint();
  const { isRetryButtonShown, isMessagesSpacingActive, setIsMessagesSpacingActive } = useChats();
  const { sendMessage } = useMessageSender();
  const isWaitingForMessage = isSubmitting || messages.length === 0;

  const {
    hasAddress,
    findAddresses,
    saveAddress: saveAddressToStore,
    fetchProperties,
    accessInstruction,
    saveAccessInstruction,
    addressValue,
  } = useWidgets();

  useEffect(() => {
    const sentinel = sentinelRef.current;

    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting && hasMore && !isLoading) {
            onLoadMore?.();
          }
        });
      },
      {
        root: containerRef.current,
        threshold: 0.1,
      }
    );

    if (sentinel) {
      observer.observe(sentinel);
    }

    return () => {
      if (sentinel) {
        observer.unobserve(sentinel);
      }
    };
  }, [hasMore, isLoading, onLoadMore]);

  useEffect(() => {
    setTimeout(() => {
      if (anchorRef?.current && (messages.length > prevMessagesLengthRef.current || isWaitingForMessage)) {
        anchorRef.current.scrollIntoView();
        prevMessagesLengthRef.current = messages.length;
      }
    }, 0);
  }, [messages, isWaitingForMessage, anchorRef, optimisticMessage]);

  useEffect(() => {
    const loadProperties = async () => {
      const token = await getToken();
      if (token) {
        fetchProperties(token);
      }
    };

    loadProperties();
  }, [getToken, fetchProperties]);

  const checkJobSummaryStatus = useCallback(async () => {
    if (messages[0]?.additionalData?.jobSummary?.jobId) {
      try {
        const token = await getToken();
        if (!token) return;
        const jobId = messages[0].additionalData.jobSummary.jobId;
        const response = await fetchJob({
          jobId: Number(jobId),
          token,
        });
        const jobStatus = response.status;
        const isConfirmed = jobStatus === 'user_accepted';
        setJobSummaryConfirmed(isConfirmed);
      } catch (error) {
        console.error('Error checking job status:', error);
        setJobSummaryConfirmed(false);
      }
    } else {
      setJobSummaryConfirmed(false);
    }
  }, [messages, getToken, setJobSummaryConfirmed]);

  useEffect(() => {
    checkJobSummaryStatus();
  }, [checkJobSummaryStatus]);

  useEffect(() => {
    setIsMessagesSpacingActive(false);
  }, [chatId]);

  // eslint-disable-next-line react-hooks/exhaustive-deps
  const debouncedAutocomplete = useCallback(
    debounce(async (query: string) => {
      const token = await getToken();
      if (token) {
        return findAddresses(query, token);
      }
      return [] as object;
    }, 500),
    [getToken, findAddresses]
  );

  const handleAutocomplete = async (query: string): Promise<object> => {
    const result = await debouncedAutocomplete(query);
    return result || ([] as object);
  };

  const handleSavePhoneNumber = async (phoneNumber: string) => {
    if (!user) return;
    try {
      const res = await user.createPhoneNumber({ phoneNumber });
      await user.reload();
      const phoneObj = user.phoneNumbers.find((a) => a.id === res.id);
      if (!phoneObj) throw new Error('Phone number not found after creation');

      await phoneObj.prepareVerification();
      return true;
    } catch (err) {
      console.error('Error saving phone number:', err);
      return false;
    }
  };

  const handleSendVerificationCode = async (phoneNumber: string) => {
    if (!user) return false;
    try {
      let phoneObj = user.phoneNumbers.find((a) => a.phoneNumber === phoneNumber);
      const newPhoneObj = !phoneObj ? await user.createPhoneNumber({ phoneNumber }) : null;

      await user.reload();
      phoneObj = user.phoneNumbers.find(
        (a) => a.id === newPhoneObj?.id || a.phoneNumber === phoneNumber
      );
      if (!phoneObj) throw new Error('Phone number not found after creation');

      await phoneObj.prepareVerification();
      return true;
    } catch (err) {
      console.error('Error sending verification code:', err);
      return false;
    }
  };

  const handleVerifyCode = async (code: string) => {
    if (!user) return false;
    try {
      const phoneObj = user.phoneNumbers[0];
      if (!phoneObj) throw new Error('Phone number not found');

      await phoneObj.attemptVerification({ code });
      await user.reload();

      return phoneObj?.verification?.status === 'verified';
    } catch (err) {
      console.error('Error verifying code:', err);
      return false;
    }
  };

  const handleSaveAddress = async (value: string | WidgetAddressFields) => {
    const token = await getToken();
    if (token) {
      try {
        await saveAddressToStore(value, token);

        if (anchorRef?.current) {
          anchorRef.current.scrollIntoView();
        }
      } catch (error) {
        console.error('Error saving address:', error);
      }
    }
  };

  const handleSaveAccessInstruction = async (value: string) => {
    const token = await getToken();
    if (token) {
      try {
        await saveAccessInstruction(value, token);

        if (anchorRef?.current) {
          anchorRef.current.scrollIntoView();
        }
      } catch (error) {
        console.error('Error saving access instruction:', error);
      }
    }
  };

  const handleClickRetry = async () => {
    const { value, attachmentsList } = isRetryButtonShown
      ? {
          value: optimisticMessage?.content,
          attachmentsList: optimisticMessage?.attachments,
        }
      : {
          value: messages[0]?.content,
          attachmentsList: messages[0]?.attachments,
        };

    if (value) {
      await sendMessage(value, attachmentsList);
    }
  };

  const scrollToBottom = useCallback(() => {
    if (anchorRef?.current) {
      anchorRef.current.scrollIntoView();
    }
  }, [anchorRef]);

  const wrapperHeight = useMemo(() => {
    const { optimistic = 0, lastSystem = 0 } = messageHeight || {};
    const height = optimistic + lastSystem;
    const heightWithAttachments = optimisticMessage?.attachments?.length ? height + 112 : height;
    const totalHeight = isDesktop ? heightWithAttachments : heightWithAttachments - 16;

    return totalHeight && !isRetryButtonShown && isMessagesSpacingActive
      ? `calc(-240px + 100dvh - ${totalHeight}px)`
      : 0;
  }, [
    messageHeight?.optimistic,
    optimisticMessage?.attachments?.length,
    messageHeight?.lastSystem,
    isDesktop,
    isRetryButtonShown,
    isMessagesSpacingActive,
  ]);

  return (
    <div ref={containerRef} className={styles.messagesContainer}>
      <div className={styles.messagesWrapper}>
        <div ref={anchorRef}></div>
        <div
          style={{
            height: wrapperHeight,
          }}
        ></div>
        {isRetryButtonShown && (
          <Alert
            inline
            color={AlertColor.WARNING}
            headingText="It looks like the service is temporarily unavailable, please try again in a few minutes"
            button={
              <Button
                type={ButtonType.PRIMARY}
                color={ButtonColor.ORANGE}
                size={ButtonSize.XS}
                onClick={handleClickRetry}
              >
                Retry
              </Button>
            }
          />
        )}
        {isWaitingForMessage && <Message isAIThinking={isSubmitting} />}
        {isWaitingForMessage && optimisticMessage && (
          <Message
            key={'__OptimisticMessage__'}
            message={{
              id: -1,
              content: optimisticMessage.content,
              attachments: optimisticMessage.attachments,
              type: MessageTypeValue.Text,
              timestamp: new Date().toISOString(),
              senderType: 'user',
              isOptimistic: true,
            }}
          />
        )}
        {!isWaitingForMessage && messages.map((message, index) => {
          const jobSummary = message.additionalData?.jobSummary;
          const isMostRecentJobSummary =
            jobSummary && !messages.slice(0, index).some((m) => m.additionalData?.jobSummary);

          if (isMostRecentJobSummary) {
            return (
              <div key={`${message.id}${message.timestamp}`}>
                <Message
                  message={{
                    type: MessageTypeValue.Text,
                    senderType: 'system',
                    timestamp: new Date().toISOString(),
                    id: Date.now(),
                    content: 'Can you share your phone number with me?',
                  }}
                >
                  <div className={styles.multiFieldInputContainer}>
                    <MultiFieldInput
                      type={MultiFieldInputType.PHONE}
                      onSave={(value) => {
                        if (typeof value === 'string') {
                          handleSavePhoneNumber(value);
                        }
                      }}
                      onSendVerificationCode={handleSendVerificationCode}
                      onVerifyCode={handleVerifyCode}
                      placeholder="+44XXXXXXXXXX"
                      value={user?.phoneNumbers.length ? user?.phoneNumbers[0].phoneNumber : ''}
                      disabled={
                        !isLoaded || user?.phoneNumbers?.[0]?.verification?.status === 'verified'
                      }
                    />
                  </div>
                </Message>
                {!!user?.phoneNumbers?.length &&
                  user?.phoneNumbers?.[0]?.verification?.status === 'verified' && (
                    <Message
                      message={{
                        type: MessageTypeValue.Text,
                        senderType: 'user',
                        timestamp: new Date().toISOString(),
                        content: user?.phoneNumbers[0].phoneNumber,
                        id: Date.now(),
                      }}
                    />
                  )}
                <Message
                  message={{
                    type: MessageTypeValue.Text,
                    senderType: 'system',
                    timestamp: new Date().toISOString(),
                    id: Date.now(),
                    content: 'Can you please share your address?',
                  }}
                >
                  <div className={styles.multiFieldInputContainer}>
                    <MultiFieldInput
                      type={MultiFieldInputType.ADDRESS}
                      onAutocomplete={handleAutocomplete}
                      onSave={(value) => handleSaveAddress(value)}
                      disabled={hasAddress}
                    />
                  </div>
                </Message>
                {addressValue && (
                  <Message
                    message={{
                      type: MessageTypeValue.Text,
                      senderType: 'user',
                      timestamp: new Date().toISOString(),
                      content: addressValue,
                      id: Date.now(),
                    }}
                  />
                )}
                {hasAddress && (
                  <>
                    <Message
                      message={{
                        id: Date.now(),
                        content:
                          'Is there anything the professional needs to know to access your home easily? Things like gate codes, key safes, or where they should park their car.',
                        type: MessageTypeValue.Text,
                        timestamp: new Date().toISOString(),
                        senderType: 'system',
                      }}
                    >
                      <div className={styles.multiFieldInputContainer}>
                        <MultiFieldInput
                          type={MultiFieldInputType.ACCESS_INSTRUCTION}
                          onSave={(value) => handleSaveAccessInstruction(value as string)}
                          disabled={!hasAddress || !!accessInstruction}
                        />
                      </div>
                    </Message>
                    {!!accessInstruction && (
                      <Message
                        message={{
                          type: MessageTypeValue.Text,
                          senderType: 'user',
                          timestamp: new Date().toISOString(),
                          content: accessInstruction,
                          id: Date.now(),
                        }}
                      />
                    )}
                  </>
                )}
                {!!user?.phoneNumbers?.length &&
                  user?.phoneNumbers?.[0]?.verification?.status === 'verified' &&
                  !!addressValue &&
                  !!accessInstruction && (
                    <Message
                      message={message}
                      messages={messages}
                      scrollToBottom={scrollToBottom}
                    />
                  )}
                {jobSummaryConfirmed && (
                  <>
                    <Message
                      key={Date.now() + Date.now()}
                      message={{
                        id: Date.now() + Date.now(),
                        content: 'Yes, that looks good',
                        attachments: [],
                        type: MessageTypeValue.Text,
                        timestamp: new Date().toISOString(),
                        senderType: 'user',
                      }}
                    />
                    <Message
                      key={Date.now()}
                      message={{
                        id: Date.now(),
                        content: `Your job request is now in progress. I'm reaching out to trusted professionals and you'll receive an email with quotes soon.
                        Next steps:
                        - Keep an eye on your inbox for updates.
                        - If you have questions, just reply to my email.
                        I'll be in touch soon!`,
                        attachments: [],
                        type: MessageTypeValue.Text,
                        timestamp: new Date().toISOString(),
                        senderType: 'system',
                      }}
                    />
                  </>
                )}
              </div>
            );
          }

          return (
            <div key={`${message.id}${message.timestamp}`}>
              <Message message={message} messages={messages} scrollToBottom={scrollToBottom} />
            </div>
          );
        })}
        {hasMore && <div ref={sentinelRef} style={{ height: 20 }} />}
      </div>
    </div>
  );
};
