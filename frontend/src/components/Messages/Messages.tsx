'use client';

import React, { useCallback, useEffect, useMemo, useRef, useState, memo } from 'react';
import { Message } from '@/components/Message';
import styles from './Messages.module.scss';
import { MessagesProps } from './Messages.types';
import { MessageTypeValue } from '@/types/messages';
import { MultiFieldInput } from '@/components/MultiFieldInput/MultiFieldInput';
import { MultiFieldInputType } from '@/components/MultiFieldInput/MultiFieldInput.types';
import { useAuth, useUser } from '@clerk/nextjs';
import { debounce } from 'lodash';
import { AddressFields as WidgetAddressFields, useWidgets } from '@/hooks/useWidgets';
import { useMessages } from '@/hooks/useMessages';
import { fetchJob } from '@/api/jobs';
import { useBreakpoint } from '@/utils/breakpointUtils';
import { Alert } from '../Alert/Alert';
import { AlertColor } from '../Alert/Alert.types';
import { Button } from '../Button';
import { ButtonColor, ButtonSize, ButtonType } from '../Button/Button.types';
import { useChats } from '@/hooks/useChats';
import useChatParams from '@/hooks/useChatParams';
import useMessageSender from '@/hooks/useMessageSender';
import { mapDocumentsToAttachments } from '@/utils/messageUtils';
import { fetchDocuments } from '@/api/documents';
import { useStreamingState } from '@/hooks/useStreamingState';

interface MessageDocument {
  id: number;
  category?: string;
}

interface MessageWithDocuments {
  id: number;
  senderType: 'user' | 'system' | 'customer_support';
  documents?: MessageDocument[];
}

// Memoized component for handling notification tags per message
const NotificationTagManager: React.FC<{
  messageId: number;
  messageNotificationTags: Record<number, boolean>;
  messages: MessageWithDocuments[];
  children: (showNotificationTag: boolean) => React.ReactNode;
}> = memo(({ messageId, messageNotificationTags, messages, children }) => {
  const showNotificationTag = useMemo(() => {
    // Find the message and its index
    const messageIndex = messages.findIndex((msg) => msg.id === messageId);
    const message = messages[messageIndex];

    if (!message || message.senderType !== 'system' || messageIndex >= messages.length - 1) {
      return false;
    }

    // Check the previous user message (next in array = previous chronologically)
    const prevMsg = messages[messageIndex + 1];
    if (!prevMsg || prevMsg.senderType !== 'user') {
      return false;
    }

    // Check both the updated message documents and the notification tag state
    const hasExistingRelevantDocs = Array.isArray(prevMsg.documents) &&
      prevMsg.documents.some((doc: MessageDocument) =>
        ['appliance', 'files', 'property details'].includes(
          doc.category?.toLowerCase?.() || ''
        )
      );

    const hasNotificationTag = messageNotificationTags[prevMsg.id] || false;

    return hasExistingRelevantDocs || hasNotificationTag;
  }, [messageId, messageNotificationTags, messages]);

  return <>{children(showNotificationTag)}</>;
});

NotificationTagManager.displayName = 'NotificationTagManager';

export const Messages: React.FC<MessagesProps> = ({
  messages,
  isLoading,
  hasMore,
  isSubmitting,
  onLoadMore,
  anchorRef,
  optimisticMessage,
}): JSX.Element => {
  const { jobSummaryConfirmed, setJobSummaryConfirmed, messageHeight } = useMessages();
  const sentinelRef = useRef(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const prevMessagesLengthRef = useRef(messages.length);
  const { getToken } = useAuth();
  const { chatId } = useChatParams();
  const { isLoaded, user } = useUser();
  const { isDesktop } = useBreakpoint();
  const { isRetryButtonShown, isMessagesSpacingActive, setIsMessagesSpacingActive, updateMessageDocuments } = useChats();
  const { sendMessage } = useMessageSender();
  const { isStreamingMessage } = useStreamingState();
  const isWaitingForMessage = isSubmitting || messages.length === 0;

  // Document polling state
  const pollingIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const POLLING_INTERVAL = 5000; // 5 seconds

  // Notification tag state for each message - locked during streaming to prevent flickering
  const [messageNotificationTags, setMessageNotificationTags] = useState<Record<number, boolean>>({});

  // Lock updates during streaming to prevent flickering
  const [lockedNotificationTags, setLockedNotificationTags] = useState<Record<number, boolean>>({});
  const isStreamingRef = useRef(false);

  // Stable messages during streaming to prevent re-renders
  const [streamingMessages, setStreamingMessages] = useState<typeof messages>([]);
  const messagesRef = useRef(messages);

  // Keep messages ref updated
  useEffect(() => {
    messagesRef.current = messages;
  }, [messages]);

  // Track streaming state - capture messages only when streaming starts
  useEffect(() => {
    if (isStreamingMessage && !isStreamingRef.current) {
      // Started streaming - lock current notification tags and capture current messages
      isStreamingRef.current = true;
      setLockedNotificationTags(messageNotificationTags);

      // Create stable user message for streaming - use optimistic message
      let stableUserMessage = null;

      if (optimisticMessage) {
        // Use optimistic message as stable user message
        stableUserMessage = {
          id: -1, // Temporary ID for optimistic message
          content: optimisticMessage.content,
          attachments: optimisticMessage.attachments || [],
          type: MessageTypeValue.Text,
          timestamp: new Date().toISOString(),
          senderType: 'user' as const,
          isOptimistic: true,
        };
      } else if (messagesRef.current.length > 1) {
        // Fallback to second message if no optimistic message
        stableUserMessage = messagesRef.current[1];
      }

      // Store only the stable user message
      setStreamingMessages(stableUserMessage ? [stableUserMessage] : []);
    } else if (!isStreamingMessage && isStreamingRef.current) {
      // Finished streaming - unlock and apply any pending updates
      isStreamingRef.current = false;
      setLockedNotificationTags({});
      setStreamingMessages([]);
    }
  }, [isStreamingMessage, messageNotificationTags, optimisticMessage]);

  // Use locked tags during streaming, regular tags otherwise
  const activeNotificationTags = isStreamingMessage ? lockedNotificationTags : messageNotificationTags;

  // During streaming, use stable messages to prevent re-renders
  const activeMessages = useMemo(() => {
    if (isStreamingMessage && streamingMessages.length > 0) {
      // During streaming: use live AI response + stable user message
      const result = [];

      // Add live AI response (first message in current messages array)
      if (messages.length > 0) {
        result.push(messages[0]); // Live streaming AI message
      }

      // Add stable user message (stored in streamingMessages[0])
      result.push(streamingMessages[0]); // Stable user message from optimistic

      return result;
    }
    // Normal operation: render all messages
    return messages;
  }, [isStreamingMessage, streamingMessages, messages]);

  // Reset notification tags when chat changes
  useEffect(() => {
    if (!isStreamingMessage) {
      setMessageNotificationTags({});
      setLockedNotificationTags({});
    }
  }, [chatId, isStreamingMessage]);

  // Index of user messages with their document IDs for quick lookup
  const userMessagesIndex = useMemo(() => {
    const index: Record<number, number[]> = {};
    messages.forEach((message) => {
      if (message.senderType === 'user' && Array.isArray(message.documents)) {
        index[message.id] = message.documents.map(doc => doc.id);
      }
    });
    return index;
  }, [messages]);

  const {
    hasAddress,
    findAddresses,
    saveAddress: saveAddressToStore,
    fetchProperties,
    accessInstruction,
    saveAccessInstruction,
    addressValue,
  } = useWidgets();

  // Document polling logic - pause during streaming to prevent flickering
  const pollDocuments = useCallback(async () => {
    // Skip polling if currently streaming to prevent flickering
    if (isStreamingMessage) return;

    try {
      const token = await getToken();
      if (!token) return;

      const documents = await fetchDocuments(token, 'chat', 'processingCompleted');

      // Update notification tags for all user messages
      const updatedTags: Record<number, boolean> = {};

      Object.entries(userMessagesIndex).forEach(([messageIdStr, documentIds]) => {
        const messageId = parseInt(messageIdStr, 10);

        // Get the current message to compare documents
        const currentMessage = messages.find(m => m.id === messageId);

        // Check if any of this message's documents have been updated with categories
        const relevantPolledDocs = documents.filter(doc => documentIds.includes(doc.id));
        const hasRelevantDocs = relevantPolledDocs.some((doc) =>
          ['appliance', 'files', 'property details'].includes(
            doc.category?.toLowerCase?.() || ''
          )
        );

        // Update message documents if they have been processed since last time
        if (relevantPolledDocs.length > 0 && currentMessage && chatId) {
          const currentDocuments = currentMessage.documents || [];
          const needsUpdate = relevantPolledDocs.some(polledDoc => {
            const currentDoc = currentDocuments.find(cd => cd.id === polledDoc.id);
            return !currentDoc || currentDoc.category !== polledDoc.category || currentDoc.status !== polledDoc.status;
          });

          if (needsUpdate) {
            // Update the message with fresh document data
            // Map DocumentDto to MessageDocument, handling null values
            const processedDocs = relevantPolledDocs.map(doc => ({
              id: doc.id,
              fileName: doc.fileName,
              sizeInKiloBytes: doc.sizeInKiloBytes,
              browserMimeType: doc.browserMimeType,
              createdAt: doc.createdAt,
              uploadContext: doc.uploadContext || 'chat',
              status: doc.status,
              category: doc.category || undefined,
              label: doc.label || undefined,
              hasStatusBeenDisplayed: false
            }));
            updateMessageDocuments(chatId, messageId, processedDocs);
          }
        }

        updatedTags[messageId] = hasRelevantDocs;
      });

      // Only update if not streaming
      if (!isStreamingMessage) {
        setMessageNotificationTags(updatedTags);
      }
    } catch (error) {
      console.error('Error polling documents:', error);
    }
  }, [getToken, userMessagesIndex, messages, chatId, updateMessageDocuments, isStreamingMessage]);

  // Set up document polling with streaming-aware interval
  useEffect(() => {
    if (pollingIntervalRef.current) {
      clearInterval(pollingIntervalRef.current);
    }

    pollingIntervalRef.current = setInterval(pollDocuments, POLLING_INTERVAL);
    void pollDocuments();

    return () => {
      if (pollingIntervalRef.current) {
        clearInterval(pollingIntervalRef.current);
        pollingIntervalRef.current = null;
      }
    };
  }, [pollDocuments]);

  useEffect(() => {
    const sentinel = sentinelRef.current;

    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting && hasMore && !isLoading) {
            onLoadMore?.();
          }
        });
      },
      {
        root: containerRef.current,
        threshold: 0.1,
      }
    );

    if (sentinel) {
      observer.observe(sentinel);
    }

    return () => {
      if (sentinel) {
        observer.unobserve(sentinel);
      }
    };
  }, [hasMore, isLoading, onLoadMore]);

  useEffect(() => {
    setTimeout(() => {
      if (
        anchorRef?.current &&
        (activeMessages.length > prevMessagesLengthRef.current || isWaitingForMessage)
      ) {
        anchorRef.current.scrollIntoView();
        prevMessagesLengthRef.current = activeMessages.length;
      }
    }, 0);
  }, [activeMessages, isWaitingForMessage, anchorRef, optimisticMessage]);

  useEffect(() => {
    const loadProperties = async () => {
      const token = await getToken();
      if (token) {
        fetchProperties(token);
      }
    };

    loadProperties();
  }, [getToken, fetchProperties]);

  const checkJobSummaryStatus = useCallback(async () => {
    if (activeMessages[0]?.additionalData?.jobSummary?.jobId) {
      try {
        const token = await getToken();
        if (!token) return;
        const jobId = activeMessages[0].additionalData.jobSummary.jobId;
        const response = await fetchJob({
          jobId: Number(jobId),
          token,
        });
        const jobStatus = response.status;
        const isConfirmed = jobStatus === 'user_accepted';
        setJobSummaryConfirmed(isConfirmed);
      } catch (error) {
        console.error('Error checking job status:', error);
        setJobSummaryConfirmed(false);
      }
    } else {
      setJobSummaryConfirmed(false);
    }
  }, [activeMessages, getToken, setJobSummaryConfirmed]);

  useEffect(() => {
    checkJobSummaryStatus();
  }, [checkJobSummaryStatus]);

  useEffect(() => {
    setIsMessagesSpacingActive(false);
  }, [chatId, setIsMessagesSpacingActive]);

  // eslint-disable-next-line react-hooks/exhaustive-deps
  const debouncedAutocomplete = useCallback(
    debounce(async (query: string) => {
      const token = await getToken();
      if (token) {
        return findAddresses(query, token);
      }
      return [] as object;
    }, 500),
    [getToken, findAddresses]
  );

  const handleAutocomplete = async (query: string): Promise<object> => {
    const result = await debouncedAutocomplete(query);
    return result || ([] as object);
  };

  const handleSavePhoneNumber = async (phoneNumber: string) => {
    if (!user) return;
    try {
      const res = await user.createPhoneNumber({ phoneNumber });
      await user.reload();
      const phoneObj = user.phoneNumbers.find((a) => a.id === res.id);
      if (!phoneObj) throw new Error('Phone number not found after creation');

      await phoneObj.prepareVerification();
      return true;
    } catch (err) {
      console.error('Error saving phone number:', err);
      return false;
    }
  };

  const handleSendVerificationCode = async (phoneNumber: string) => {
    if (!user) return false;
    try {
      let phoneObj = user.phoneNumbers.find((a) => a.phoneNumber === phoneNumber);
      const newPhoneObj = !phoneObj ? await user.createPhoneNumber({ phoneNumber }) : null;

      await user.reload();
      phoneObj = user.phoneNumbers.find(
        (a) => a.id === newPhoneObj?.id || a.phoneNumber === phoneNumber
      );
      if (!phoneObj) throw new Error('Phone number not found after creation');

      await phoneObj.prepareVerification();
      return true;
    } catch (err) {
      console.error('Error sending verification code:', err);
      return false;
    }
  };

  const handleVerifyCode = async (code: string) => {
    if (!user) return false;
    try {
      const phoneObj = user.phoneNumbers[0];
      if (!phoneObj) throw new Error('Phone number not found');

      await phoneObj.attemptVerification({ code });
      await user.reload();

      return phoneObj?.verification?.status === 'verified';
    } catch (err) {
      console.error('Error verifying code:', err);
      return false;
    }
  };

  const handleSaveAddress = async (value: string | WidgetAddressFields) => {
    const token = await getToken();
    if (token) {
      try {
        await saveAddressToStore(value, token);

        if (anchorRef?.current) {
          anchorRef.current.scrollIntoView();
        }
      } catch (error) {
        console.error('Error saving address:', error);
      }
    }
  };

  const handleSaveAccessInstruction = async (value: string) => {
    const token = await getToken();
    if (token) {
      try {
        await saveAccessInstruction(value, token);

        if (anchorRef?.current) {
          anchorRef.current.scrollIntoView();
        }
      } catch (error) {
        console.error('Error saving access instruction:', error);
      }
    }
  };

  const handleClickRetry = async () => {
    const { value, attachmentsList } = isRetryButtonShown
      ? {
        value: optimisticMessage?.content,
        attachmentsList: optimisticMessage?.attachments,
      }
      : {
        value: activeMessages[0]?.content,
        attachmentsList: activeMessages[0]?.attachments,
      };

    if (value) {
      await sendMessage(value, attachmentsList);
    }
  };

  const scrollToBottom = useCallback(() => {
    if (anchorRef?.current) {
      anchorRef.current.scrollIntoView();
    }
  }, [anchorRef]);

  const wrapperHeight = useMemo(() => {
    const { optimistic = 0, lastSystem = 0 } = messageHeight || {};
    const height = optimistic + lastSystem;
    const heightWithAttachments = optimisticMessage?.attachments?.length ? height + 112 : height;
    const totalHeight = isDesktop ? heightWithAttachments : heightWithAttachments - 16;

    return totalHeight && !isRetryButtonShown && isMessagesSpacingActive
      ? `calc(-240px + 100dvh - ${totalHeight}px)`
      : 0;
  }, [
    messageHeight,
    optimisticMessage?.attachments?.length,
    isDesktop,
    isRetryButtonShown,
    isMessagesSpacingActive,
  ]);

  return (
    <div ref={containerRef} className={styles.messagesContainer}>
      <div className={styles.messagesWrapper}>
        <div ref={anchorRef}></div>
        <div
          style={{
            height: wrapperHeight,
          }}
        ></div>
        {isRetryButtonShown && (
          <Alert
            inline
            color={AlertColor.WARNING}
            headingText="It looks like the service is temporarily unavailable, please try again in a few minutes"
            button={
              <Button
                type={ButtonType.PRIMARY}
                color={ButtonColor.ORANGE}
                size={ButtonSize.XS}
                onClick={handleClickRetry}
              >
                Retry
              </Button>
            }
          />
        )}
        {isWaitingForMessage && <Message isAIThinking={isSubmitting} />}
        {isWaitingForMessage && optimisticMessage && (
          <Message
            key={'__OptimisticMessage__'}
            message={{
              id: -1,
              content: optimisticMessage.content,
              attachments: mapDocumentsToAttachments(optimisticMessage?.attachments),
              type: MessageTypeValue.Text,
              timestamp: new Date().toISOString(),
              senderType: 'user',
              isOptimistic: true,
            }}
          />
        )}
        {!isWaitingForMessage &&
          activeMessages.map((message, index) => {
            const jobSummary = message.additionalData?.jobSummary;
            const isMostRecentJobSummary =
              jobSummary && !activeMessages.slice(0, index).some((m) => m.additionalData?.jobSummary);

            // Create stable key during streaming to prevent unnecessary re-renders
            const messageKey = `${message.id}${message.timestamp}`;

            if (isMostRecentJobSummary) {
              return (
                <div key={messageKey}>
                  <Message
                    message={{
                      type: MessageTypeValue.Text,
                      senderType: 'system',
                      timestamp: new Date().toISOString(),
                      id: Date.now(),
                      content: 'Can you share your phone number with me?',
                    }}
                  >
                    <div className={styles.multiFieldInputContainer}>
                      <MultiFieldInput
                        type={MultiFieldInputType.PHONE}
                        onSave={(value) => {
                          if (typeof value === 'string') {
                            handleSavePhoneNumber(value);
                          }
                        }}
                        onSendVerificationCode={handleSendVerificationCode}
                        onVerifyCode={handleVerifyCode}
                        placeholder="+44XXXXXXXXXX"
                        value={user?.phoneNumbers.length ? user?.phoneNumbers[0].phoneNumber : ''}
                        disabled={
                          !isLoaded || user?.phoneNumbers?.[0]?.verification?.status === 'verified'
                        }
                      />
                    </div>
                  </Message>
                  {!!user?.phoneNumbers?.length &&
                    user?.phoneNumbers?.[0]?.verification?.status === 'verified' && (
                      <Message
                        message={{
                          type: MessageTypeValue.Text,
                          senderType: 'user',
                          timestamp: new Date().toISOString(),
                          content: user?.phoneNumbers[0].phoneNumber,
                          id: Date.now(),
                        }}
                      />
                    )}
                  <Message
                    message={{
                      type: MessageTypeValue.Text,
                      senderType: 'system',
                      timestamp: new Date().toISOString(),
                      id: Date.now(),
                      content: 'Can you please share your address?',
                    }}
                  >
                    <div className={styles.multiFieldInputContainer}>
                      <MultiFieldInput
                        type={MultiFieldInputType.ADDRESS}
                        onAutocomplete={handleAutocomplete}
                        onSave={(value) => handleSaveAddress(value)}
                        disabled={hasAddress}
                      />
                    </div>
                  </Message>
                  {addressValue && (
                    <Message
                      message={{
                        type: MessageTypeValue.Text,
                        senderType: 'user',
                        timestamp: new Date().toISOString(),
                        content: addressValue,
                        id: Date.now(),
                      }}
                    />
                  )}
                  {hasAddress && (
                    <>
                      <Message
                        message={{
                          id: Date.now(),
                          content:
                            'Is there anything the professional needs to know to access your home easily? Things like gate codes, key safes, or where they should park their car.',
                          type: MessageTypeValue.Text,
                          timestamp: new Date().toISOString(),
                          senderType: 'system',
                        }}
                      >
                        <div className={styles.multiFieldInputContainer}>
                          <MultiFieldInput
                            type={MultiFieldInputType.ACCESS_INSTRUCTION}
                            onSave={(value) => handleSaveAccessInstruction(value as string)}
                            disabled={!hasAddress || !!accessInstruction}
                          />
                        </div>
                      </Message>
                      {!!accessInstruction && (
                        <Message
                          message={{
                            type: MessageTypeValue.Text,
                            senderType: 'user',
                            timestamp: new Date().toISOString(),
                            content: accessInstruction,
                            id: Date.now(),
                          }}
                        />
                      )}
                    </>
                  )}
                  {!!user?.phoneNumbers?.length &&
                    user?.phoneNumbers?.[0]?.verification?.status === 'verified' &&
                    !!addressValue &&
                    !!accessInstruction && (
                      <NotificationTagManager messageId={message.id} messageNotificationTags={activeNotificationTags} messages={activeMessages}>
                        {(showNotificationTag) => (
                          <Message
                            message={message}
                            messages={activeMessages}
                            scrollToBottom={scrollToBottom}
                            showNotificationTag={showNotificationTag}
                          />
                        )}
                      </NotificationTagManager>
                    )}
                  {jobSummaryConfirmed && (
                    <>
                      <Message
                        key={Date.now() + Date.now()}
                        message={{
                          id: Date.now() + Date.now(),
                          content: 'Yes, that looks good',
                          attachments: [],
                          type: MessageTypeValue.Text,
                          timestamp: new Date().toISOString(),
                          senderType: 'user',
                        }}
                      />
                      <Message
                        key={Date.now()}
                        message={{
                          id: Date.now(),
                          content: `Your job request is now in progress. I'm reaching out to trusted professionals and you'll receive an email with quotes soon.
                        Next steps:
                        - Keep an eye on your inbox for updates.
                        - If you have questions, just reply to my email.
                        I'll be in touch soon!`,
                          attachments: [],
                          type: MessageTypeValue.Text,
                          timestamp: new Date().toISOString(),
                          senderType: 'system',
                        }}
                      />
                    </>
                  )}
                </div>
              );
            }

            return (
              <div key={messageKey}>
                <NotificationTagManager messageId={message.id} messageNotificationTags={activeNotificationTags} messages={activeMessages}>
                  {(showNotificationTag) => (
                    <Message
                      message={message}
                      messages={activeMessages}
                      scrollToBottom={scrollToBottom}
                      showNotificationTag={showNotificationTag}
                    />
                  )}
                </NotificationTagManager>
              </div>
            );
          })}
        {hasMore && <div ref={sentinelRef} style={{ height: 20 }} />}
      </div>
    </div>
  );
};
