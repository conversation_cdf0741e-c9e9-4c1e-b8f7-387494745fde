import React, { useEffect, useState, useCallback } from 'react';
import styles from './OnboardingSteps.module.scss';
import { Radio } from '@/components/Radio/Radio';
import { RadioState } from '@/components/Radio/Radio.types';
import { MultiFieldInput } from '@/components/MultiFieldInput/MultiFieldInput';
import {
  MultiFieldInputType,
  MultiFieldInputMode,
  Option,
} from '@/components/MultiFieldInput/MultiFieldInput.types';
import { Button } from '@/components/Button/Button';
import { ButtonColor, ButtonSize, ButtonType, ButtonState } from '@/components/Button/Button.types';
import classNames from 'classnames';
import { debounce } from 'lodash';

export interface UserRoleOption {
  id: string;
  title: string;
  description: string;
}

export interface OnboardingStepsProps {
  onRoleSelected?: (role: string) => void;
  onAddressSelected?: (
    address: string | { line1: string; line2: string; city: string; postcode: string },
    displayAddress?: string
  ) => void;
  selectedAddress?:
  | string
  | { line1: string; line2: string; city: string; postcode: string }
  | null;
  step?: number;
  selectedPropertyType?: string | null;
  selectedOwnershipType?: string | null;
  onPropertyTypeSelected?: (type: string) => void;
  onOwnershipTypeSelected?: (type: string) => void;
  selectedRole?: string | null;
  forceDropdownPosition?: 'top' | 'bottom';
}

const userRoleOptions: UserRoleOption[] = [
  {
    id: 'owner',
    title: 'Owner and occupier',
    description: 'You own the property and also occupy or live in it',
  },
  {
    id: 'landlord',
    title: 'Landlord',
    description: 'You own the property and tenant it out for rent',
  },
  {
    id: 'tenant',
    title: 'Tenant',
    description: 'You rent this property from a landlord',
  },
  {
    id: 'manager',
    title: 'Property management professional',
    description: 'You work at a lettings or property management company',
  },
];

const propertyTypes = ['House', 'Flat', 'Office', 'Retail'];
const ownershipTypes = ['Freehold', 'Leasehold', 'Share of freehold'];

export const OnboardingSteps: React.FC<OnboardingStepsProps> = ({
  onRoleSelected,
  onAddressSelected,
  selectedAddress,
  step = 0,
  selectedPropertyType,
  selectedOwnershipType,
  onPropertyTypeSelected,
  onOwnershipTypeSelected,
  selectedRole,
  forceDropdownPosition,
}) => {
  const [currentMode, setCurrentMode] = useState(() => {
    if (selectedAddress) {
      return typeof selectedAddress === 'string'
        ? MultiFieldInputMode.SELECT
        : MultiFieldInputMode.MANUAL;
    }
    return MultiFieldInputMode.SELECT;
  });

  const [displayAddress, setDisplayAddress] = useState<string | null>(null);

  useEffect(() => {
    if (selectedAddress) {
      if (typeof selectedAddress === 'string') {
        setCurrentMode(MultiFieldInputMode.SELECT);
      } else {
        setCurrentMode(MultiFieldInputMode.MANUAL);
      }
    }
  }, [selectedAddress]);

  const debouncedAddressSelected = useCallback(
    debounce((address) => {
      if (onAddressSelected) {
        onAddressSelected(address);
      }
    }, 500),
    [onAddressSelected]
  );

  const handleRoleChange = (role: string) => {
    if (onRoleSelected) {
      onRoleSelected(role);
    }
  };

  const handleSwitchToManual = () => {
    setCurrentMode(MultiFieldInputMode.MANUAL);
  };

  const handleSelectOption = (option: Option) => {
    setDisplayAddress(option.label);
    if (onAddressSelected) {
      onAddressSelected(option.value, option.label);
    }
  };

  const handleAddressChange = (
    value: string | { line1: string; line2: string; city: string; postcode: string }
  ) => {
    if (currentMode === MultiFieldInputMode.MANUAL) {
      debouncedAddressSelected(value);
    }
  };

  if (step === 1) {
    let subtitle = '';
    switch (selectedRole) {
      case 'owner':
        subtitle = 'Find your address';
        break;
      case 'landlord':
        subtitle = 'Find the property that you rent out';
        break;
      case 'tenant':
        subtitle = 'Find the property that you rent and live in';
        break;
      case 'manager':
        subtitle = 'Find the property that you manage';
        break;
      default:
        subtitle = '';
    }

    return (
      <div className={styles.container}>
        <h2 className={styles.title}>
          {selectedRole === 'landlord' ? 'Property address' : 'Your address'}
        </h2>
        <p className={styles.subtitle}>{subtitle}</p>
        <div className={styles.inputContainer}>
          <MultiFieldInput
            type={MultiFieldInputType.ADDRESS}
            title=""
            value={
              displayAddress || selectedAddress || { line1: '', line2: '', city: '', postcode: '' }
            }
            onChange={handleAddressChange}
            placeholder="Find address"
            manualEntryText="Enter address manually"
            hideBorder
            hideSaveButton
            forceDropdownPosition={forceDropdownPosition}
            initialMode={currentMode}
            onSwitchToManual={handleSwitchToManual}
            onSelectOption={handleSelectOption}
            hideTitle
          />
        </div>
      </div>
    );
  }

  if (step === 2) {
    return (
      <div className={styles.container}>
        <h2 className={styles.title}>Property type</h2>
        <div className={styles.subtitle}>What type of property is this?</div>
        <div className={styles.propertyTypeContainer}>
          {propertyTypes.map((type) => (
            <div
              key={type}
              className={classNames(styles.radioOption, {
                [styles.selected]: selectedPropertyType === type,
              })}
            >
              <Radio
                labelText={type}
                checked={selectedPropertyType === type}
                onChange={() => onPropertyTypeSelected && onPropertyTypeSelected(type)}
                state={RadioState.NORMAL}
                name="propertyType"
                value={type}
              />
            </div>
          ))}
        </div>

        {selectedRole !== 'tenant' && selectedRole !== 'manager' && (
          <>
            <div className={styles.subtitle}>What type of ownership do you have?</div>
            <div>
              {ownershipTypes.map((type) => (
                <div
                  key={type}
                  className={classNames(styles.radioOption, {
                    [styles.selected]: selectedOwnershipType === type,
                  })}
                >
                  <Radio
                    labelText={type}
                    checked={selectedOwnershipType === type}
                    onChange={() => onOwnershipTypeSelected && onOwnershipTypeSelected(type)}
                    state={RadioState.NORMAL}
                    name="ownershipType"
                    value={type}
                  />
                </div>
              ))}
            </div>
          </>
        )}
      </div>
    );
  }

  if (step === 3) {
    return (
      <div className={`${styles.container} ${styles.completionContainer}`}>
        <img
          src="/House_8x.png"
          alt="House"
          className={styles.completionImage}
          srcSet="/House_8x.png 8x"
        />
        <h2 className={styles.completionTitle}>Account set up complete</h2>
        <div className={styles.completionDescription}>
          Tell us more or upload documents to build your Property Profile in order to unlock even
          more personalised assistance from Hey Alfie
        </div>
        <div className={styles.buttonGroup}>
          <Button
            type={ButtonType.PRIMARY}
            color={ButtonColor.GREEN_PRIMARY}
            size={ButtonSize.L}
            className={styles.button}
            onClick={() => { }}
            state={ButtonState.DISABLED}
          >
            Build my Property Profile (coming soon)
          </Button>
          <Button
            type={ButtonType.SECONDARY}
            color={ButtonColor.GREEN_PRIMARY}
            size={ButtonSize.L}
            className={styles.button}
            href="/"
          >
            Go to my account
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className={styles.container}>
      <h2 className={styles.title}>How will you use Hey Alfie?</h2>
      <p className={styles.subtitle}>Select the option that describes your situation</p>
      <p className={styles.helperText}>
        If you have multiple properties, select the option that best describes your main usage of
        Hey Alfie
      </p>

      <div className={styles.optionsContainer}>
        {userRoleOptions.map((option) => (
          <div
            key={option.id}
            className={classNames(styles.radioOption, {
              [styles.selected]: selectedRole === option.id,
            })}
          >
            <Radio
              labelText={option.title}
              helperText={option.description}
              checked={selectedRole === option.id}
              onChange={() => handleRoleChange(option.id)}
              state={RadioState.NORMAL}
              name="userRole"
              value={option.id}
            />
          </div>
        ))}
      </div>
    </div>
  );
};
