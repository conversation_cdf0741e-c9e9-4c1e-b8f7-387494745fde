'use client';

import React, { MouseEvent, useEffect, useRef, useState } from 'react';
import Link from 'next/link';
import classNames from 'classnames';
import { IconSvgObject } from '@hugeicons/react';
import * as StrokeStandard from '@hugeicons-pro/core-stroke-standard';
import { SignInButton, SignUpButton, useAuth } from '@clerk/nextjs';
import { SidebarProps } from './Sidebar.types';
import styles from './Sidebar.module.scss';
import { Button } from '../Button';
import { ButtonColor, ButtonSize, ButtonState, ButtonType } from '../Button/Button.types';
import { Logo } from './components/Logo/Logo';
import { HugeiconsIcon } from '../HugeiconsIcon';
import { useChats } from '@/hooks/useChats';
import { usePathname, useRouter } from 'next/navigation';
import { NavigationItem } from './components/NavigationItem/NavigationItem';
import { useSidebar } from '@/hooks/useSidebar';
import { useBreakpoint } from '@/utils/breakpointUtils';
import { useWidgets } from '@/hooks/useWidgets';
import useChatParams from '@/hooks/useChatParams';

export const Sidebar: React.FC<SidebarProps> = (
  {
    // onRename,
    // onPin,
    // onShare,
    // onDelete,
  }
) => {
  const { isSignedIn: isAuthenticated, getToken } = useAuth();
  const { chats, fetchChats, hasMore, isLoading, loadMoreChats } = useChats();
  const [isSettingsOpen, setIsSettingsOpen] = useState(false);
  const sidebarRef = useRef<HTMLDivElement>(null);
  const recentChatsContainerRef = useRef<HTMLDivElement>(null);
  const sentinelRef = useRef(null);
  const { chatId: activeChatId } = useChatParams();
  const pathname = usePathname();
  const { fetchProperties } = useWidgets();

  const router = useRouter();

  const { closeSidebar, toggleSidebar } = useSidebar();
  const { isMobile } = useBreakpoint();

  useEffect(() => {
    const loadProperties = async () => {
      const token = await getToken();
      if (token) {
        fetchProperties(token);
      }
    };

    loadProperties();
  }, [getToken, fetchProperties]);

  useEffect(() => {
    const sentinel = sentinelRef.current;
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach(async (entry) => {
          if (entry.isIntersecting && hasMore && !isLoading) {
            const token = await getToken();
            if (token) {
              loadMoreChats(token);
            }
          }
        });
      },
      {
        root: recentChatsContainerRef.current,
        threshold: 0.1,
      }
    );

    if (sentinel) {
      observer.observe(sentinel);
    }

    return () => {
      if (sentinel) {
        observer.unobserve(sentinel);
      }
    };
  }, [hasMore, isLoading, loadMoreChats, getToken]);

  useEffect(() => {
    const loadChats = async () => {
      if (isAuthenticated) {
        const token = await getToken();
        if (token) {
          fetchChats(token);
        }
      }
    };

    loadChats();
  }, [isAuthenticated, fetchChats, getToken]);

  const chatsWithId = Object.values(chats).filter((chat) => 'id' in chat);

  // const chatOptions = [
  //   {
  //     label: "Rename",
  //     icon: (
  //       <HugeiconsIcon
  //         icon={StrokeStandard.PencilEdit01Icon as unknown as IconSvgObject}
  //         size={14}
  //       />
  //     ),
  //     onClick: onRename,
  //   },
  //   {
  //     label: "Pin",
  //     icon: (
  //       <HugeiconsIcon
  //         icon={StrokeStandard.PinIcon as unknown as IconSvgObject}
  //         size={14}
  //       />
  //     ),
  //     onClick: onPin,
  //   },
  //   {
  //     label: "Share",
  //     icon: (
  //       <HugeiconsIcon
  //         icon={StrokeStandard.Share05Icon as unknown as IconSvgObject}
  //         size={14}
  //       />
  //     ),
  //     onClick: onShare,
  //   },
  //   {
  //     label: "Delete",
  //     icon: (
  //       <HugeiconsIcon
  //         icon={StrokeStandard.Delete02Icon as unknown as IconSvgObject}
  //         size={14}
  //       />
  //     ),
  //     variant: "danger" as const,
  //     onClick: onDelete,
  //   },
  // ];

  const handleCreateNewChat = async (e: MouseEvent<HTMLAnchorElement, MouseEvent>) => {
    e.preventDefault();

    if (isMobile) {
      closeSidebar();
    }

    router?.push(`/`);
  };

  const navigationItems = [
    {
      icon: (
        <HugeiconsIcon icon={StrokeStandard.Home11Icon as unknown as IconSvgObject} size={20} />
      ),
      label: 'Home',
      url: '/',
      isComingSoon: false,
      onClick: handleCreateNewChat,
    },
    {
      icon: (
        <HugeiconsIcon
          icon={StrokeStandard.Briefcase02Icon as unknown as IconSvgObject}
          size={20}
        />
      ),
      label: 'Jobs',
      url: '/jobs',
      isComingSoon: true,
    },
    {
      icon: (
        <HugeiconsIcon icon={StrokeStandard.CheckListIcon as unknown as IconSvgObject} size={20} />
      ),
      label: 'To-do list',
      url: '/todo',
      isComingSoon: true,
    },
    {
      icon: (
        <HugeiconsIcon icon={StrokeStandard.Files01Icon as unknown as IconSvgObject} size={20} />
      ),
      label: 'Files',
      url: '/files',
      isComingSoon: true,
    },
  ];

  return (
    <aside className={styles.sidebar} ref={sidebarRef}>
      <div className={styles.container}>
        <div
          className={classNames(styles.header, {
            [styles.mobile]: isMobile,
          })}
        >
          <div className={styles.logoContainer}>
            {isMobile && (
              <div className={styles.burger} onClick={toggleSidebar}>
                <HugeiconsIcon
                  icon={StrokeStandard.Cancel01Icon as unknown as IconSvgObject}
                  size={24}
                  color="#6b7280"
                />
              </div>
            )}
            <div
              className={classNames(styles.logo, {
                [styles.mobile]: isMobile,
              })}
            >
              <Logo />
            </div>
          </div>
          <Button
            type={ButtonType.PRIMARY}
            color={ButtonColor.GREEN_PRIMARY}
            state={isAuthenticated ? ButtonState.DEFAULT : ButtonState.DISABLED}
            size={ButtonSize.L}
            onClick={() => {
              if (isMobile && isAuthenticated) {
                closeSidebar();
              }

              router?.push(`/`);
            }}
            showLeftIcon
            leftIconName="Add01Icon"
          >
            Create new
          </Button>
        </div>

        <div className={styles.content}>
          <nav>
            <ul className={classNames('space-y-2', styles.navContainer)}>
              {navigationItems.map((item) => (
                <li
                  key={item.url}
                  className={classNames({
                    [styles.comingSoon]: item.isComingSoon,
                  })}
                >
                  <NavigationItem
                    icon={item.icon}
                    label={item.label}
                    isAuthenticated={isAuthenticated ?? false}
                    isComingSoon={item.isComingSoon}
                    onClick={item.onClick}
                    url={item.url}
                    className={classNames({
                      [styles.navItem]: true,
                      [styles.active]: item.url === '/' && pathname === '/' && isAuthenticated,
                    })}
                  />
                </li>
              ))}
              <li
                className={classNames({
                  [styles.comingSoon]: true,
                })}
              >
                <NavigationItem
                  icon={
                    <HugeiconsIcon
                      icon={StrokeStandard.Settings01Icon as unknown as IconSvgObject}
                      size={20}
                    />
                  }
                  label="Settings"
                  isAuthenticated={isAuthenticated ?? false}
                  isComingSoon={true}
                  isButton
                  onClick={() => isAuthenticated && setIsSettingsOpen(!isSettingsOpen)}
                  className={styles.settingsItem}
                />
                {isSettingsOpen && isAuthenticated && (
                  <ul className={styles.settingsSubmenu}>
                    <li className={styles.navItem}>
                      <button>Profile</button>
                    </li>
                    <li className={styles.navItem}>
                      <button>Notification</button>
                    </li>
                    <li className={styles.navItem}>
                      <button>Privacy & data</button>
                    </li>
                    <li className={styles.navItem}>
                      <button>Help</button>
                    </li>
                  </ul>
                )}
              </li>
            </ul>
          </nav>

          {isAuthenticated && (
            <div className={styles.recentChats}>
              <h2 className={styles.recentChatsTitle}>RECENT CHATS</h2>
              <div
                className={classNames(styles.recentChatsWrapper, {
                  [styles.settingsOpen]: isSettingsOpen,
                })}
              >
                <div className={styles.recentChatsContainer} ref={recentChatsContainerRef}>
                  <ul className="space-y-1">
                    {chatsWithId.map((chat) => (
                      <li key={chat.id}>
                        <Link
                          href={`/chats/${chat.id}`}
                          onClick={(e) => {
                            e.preventDefault();

                            if (isMobile) {
                              closeSidebar();
                            }

                            router.push(`/chats/${chat.id}`);
                          }}
                          className={classNames(styles.chatItem, {
                            [styles.active]: chat.id === activeChatId,
                          })}
                        >
                          <span className={styles.chatTitle}>{chat.title}</span>
                        </Link>
                      </li>
                    ))}
                  </ul>
                  {hasMore && <div ref={sentinelRef} style={{ height: 20 }} />}
                </div>
              </div>
            </div>
          )}
        </div>

        {!isAuthenticated && (
          <div className={styles.authContainer}>
            <p className={styles.authText}>Sign up or log in to access all the features</p>
            <div className="flex gap-4">
              <SignUpButton mode="modal">
                <Button
                  type={ButtonType.PRIMARY}
                  color={ButtonColor.GREEN_PRIMARY}
                  size={ButtonSize.BASE}
                  className="flex-1"
                >
                  Sign up
                </Button>
              </SignUpButton>
              <SignInButton mode="modal">
                <Button
                  type={ButtonType.SECONDARY}
                  color={ButtonColor.GREEN_PRIMARY}
                  size={ButtonSize.BASE}
                  className="flex-1"
                >
                  Log in
                </Button>
              </SignInButton>
            </div>
          </div>
        )}
      </div>
    </aside>
  );
};
