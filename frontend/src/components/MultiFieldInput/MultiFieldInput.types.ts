import { ReactNode } from 'react';

export interface Option {
  value: string;
  label: string;
}

export enum MultiFieldInputType {
  TEXT = 'text',
  PHONE = 'phone',
  ADDRESS = 'address',
  ACCESS_INSTRUCTION = 'access_instruction',
  TEXTAREA = 'textarea',
  FINDADDRESS = 'findAddress',
  EMAIL = 'gmail',
}

export enum MultiFieldInputMode {
  SELECT = 'select',
  MANUAL = 'manual',
}

export enum PhoneVerificationState {
  INITIAL = 'initial',
  CODE_SENT = 'codeSent',
  VERIFIED = 'verified',
  ERROR = 'error',
}

export interface AddressFields {
  line1: string;
  line2: string;
  city: string;
  postcode: string;
}

export interface AddressSearchResult {
  hits?: Array<{
    id?: string;
    value?: string;
    suggestion?: string;
    label?: string;
  }>;
}
export interface MultiFieldInputProps {
  type?: MultiFieldInputType;
  title?: string;
  value?: string | AddressFields;
  placeholder?: string;
  onChange?: (value: string | AddressFields) => void;
  onSave?: (value: string | AddressFields) => void;
  saveButtonText?: string;
  className?: string;
  children?: ReactNode;
  loading?: boolean;
  error?: string;
  disabled?: boolean;
  options?: Option[];
  manualEntryText?: string;
  onSelectOption?: (option: Option) => void;
  onSwitchToManual?: () => void;
  initialMode?: MultiFieldInputMode;
  onAutocomplete?: (value: string) => Promise<object>;
  onSendVerificationCode?: (phoneNumber: string) => Promise<boolean>;
  onVerifyCode?: (code: string, phoneNumber: string) => Promise<boolean>;
  hideBorder?: boolean;
  hideSaveButton?: boolean;
  forceDropdownPosition?: 'top' | 'bottom';
  hideTitle?: boolean;
  compactMode?: boolean;
  isTextarea?: boolean;
  onValidationChange?: (isValid: boolean) => void;
}
