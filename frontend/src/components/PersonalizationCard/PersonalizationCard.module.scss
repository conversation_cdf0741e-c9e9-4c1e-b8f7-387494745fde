.container {
	background: #f8f3e9;
	border-radius: 16px;
	padding: 24px 24px 10px 24px;
	width: 100%;
	height: 100%;
	margin: 0 auto;
	box-sizing: border-box;
	box-shadow: none;
	display: flex;
	flex-direction: column;
}

.header {
	margin-bottom: 18px;
}

.title {
	font-size: 18px;
	font-weight: bold;
	color: var(--colors-black);
	margin-bottom: 10px;
}

.titleInput {
	font-size: 18px;
	font-weight: 700;
	color: var(--colors-black);
	margin-bottom: 4px;
	border: 1px solid var(--colors-gray-200);
	border-radius: 8px;
	padding: 10px 8px;
	background: var(--colors-white);
	font-family: 'Quasimoda', sans-serif;
	width: 100%;
	box-sizing: border-box;
	height: 51px;
	max-width: 458px;

	&::placeholder {
		color: #b3b3b3;
		opacity: 1;
	}

	&:focus {
		outline: none;
		border-color: #007a6e;
	}
}

.titleInputWrapper {
	max-width: 458px;
	margin-bottom: 4px;
}

.table {
	width: 100%;
	border-collapse: separate;
	margin-bottom: 16px;
}

.tableRow {
	vertical-align: middle;
}

.tableCell {
	font-size: 16px;
	color: var(--colors-black);
	vertical-align: middle;

	&:first-child {
		width: 188px;
		color: var(--colors-neutral-700);
		font-weight: bold;
	}
}

.input {
	width: 270px;
	padding: 10px 8px;
	border: 1px solid var(--colors-gray-200);
	border-radius: 8px;
	font-family: 'Quasimoda', sans-serif;
	font-size: 16px;
	color: #222;
	background: var(--colors-white);
	margin-bottom: 0;
	height: 48px;

	&::placeholder {
		color: #b3b3b3;
		opacity: 1;
	}

	&:focus {
		outline: none;
		border-color: #007a6e;
	}

	@media (max-width: 768px) {
		width: 100%;
	}
}

.inputWrapper {
	width: 270px;
	max-width: 100%;

	@media (max-width: 768px) {
		width: 100%;
	}
}

.dropdown {
	width: 270px;

	@media (max-width: 768px) {
		width: 100%;
	}
}

.radioGroup {
	display: flex;
	gap: 8px;
	flex-wrap: wrap;
	width: 270px;

	:global([class*='card']) {
		background: var(--colors-white) !important;
	}

	@media (max-width: 768px) {
		width: 100%;
	}
}

.filesSection {
	margin-bottom: 16px;
	display: flex;
	flex-wrap: wrap;
	gap: 8px;
}

.content {
	flex: 1;
}

.actions {
	display: flex;
	gap: 22px;
	margin-top: auto;
	padding-bottom: 14px;
	gap: 8px;
}

.divider {
	height: 1px;
	background: var(--colors-neutral-100);
	margin: auto 0 14px 0;
}

@media (max-width: 768px) {
	.container {
		padding: 24px 24px 0 24px;
		border-radius: 12px;
		width: 100%;
	}

	.tableCell:first-child {
		width: 110px;
		padding-right: 8px;
		white-space: nowrap;
	}

	.tableRow {
		display: block;
		margin-bottom: 8px;
	}

	.tableCell {
		display: block;
		width: 100%;
		margin-bottom: 2px;
	}

	.actions {
		gap: 8px;
		padding-bottom: 16px;
	}

	.radioGroup {
		display: flex;
		flex-wrap: nowrap;
		width: 100%;
		gap: 4px;
	}

	.radioCard {
		width: 50%;
	}
}

.transparent {
	background: transparent;
}