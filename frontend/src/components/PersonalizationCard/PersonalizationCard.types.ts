import { EditableField } from '@/hooks/useEditableFields';
import { DocumentDto } from '@/api/documents';

export type PersonalizationCardFile = DocumentDto;

export type FieldType = 'text' | 'number' | 'select' | 'radio' | 'phone';

export interface SelectOption {
  label: string;
  value: string | number;
}

export interface PhoneVerificationState {
  state: 'initial' | 'code_sent' | 'verified';
  phoneNumber: string;
  verificationCode: string;
  verificationDigits: string[];
  verificationError: string;
  resendCountdown: number;
  isResendDisabled: boolean;
}

export interface PersonalizationCardField extends EditableField {
  label: string;
  type: FieldType;
  options?: SelectOption[];
  placeholder?: string;
  radioOptions?: SelectOption[];
  phoneVerification?: PhoneVerificationState;
  onSendVerificationCode?: (phone: string) => Promise<boolean>;
  onVerifyCode?: (code: string, phone: string) => Promise<boolean>;
}

export interface PersonalizationCardProps {
  title?: string;
  fields: PersonalizationCardField[];
  files?: PersonalizationCardFile[];
  onSave?: (
    updatedFields: PersonalizationCardField[],
    files: PersonalizationCardFile[],
    title?: string
  ) => void;
  onFileRemove?: (fileId: string | number) => void;
  onFieldChange?: (fieldId: string, value: string | number) => void;
  className?: string;
  isEditing?: boolean;
  onEdit?: () => void;
  onCancel?: () => void;
  titleEditable?: boolean;
  context?: 'propertyDetails' | 'appliances';
  transparent?: boolean;
  hideActions?: boolean;
  renderAfterField?: (field: PersonalizationCardField, index: number) => React.ReactNode;
}
