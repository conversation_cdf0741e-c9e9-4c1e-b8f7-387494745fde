import React from 'react';
import { useUser } from '@clerk/nextjs';
import { Modal } from '@/components/Modal';
import { PersonalizationCard } from '@/components/PersonalizationCard';
import { MultiFieldInput, MultiFieldInputType, AddressFields } from '@/components/MultiFieldInput';
import { Button } from '@/components/Button';
import { ButtonType, ButtonColor, ButtonSize, ButtonState } from '@/components/Button/Button.types';
import styles from './UserDetailsForm.module.scss';

import { PersonalizationCardField } from '@/components/PersonalizationCard/PersonalizationCard.types';

interface UserDetailsFormProps {
  open: boolean;
  onClose: () => void;
  onSave?: (data: Record<string, any>) => void;
}

const initialFields: PersonalizationCardField[] = [
  { id: 'name', label: 'Your name', value: '', editable: true, type: 'text' },
  { id: 'email', label: '', value: '', editable: false, type: 'text' },
  { id: 'phone', label: '', value: '', editable: false, type: 'phone' },
  {
    id: 'address',
    label: '',
    value: '',
    editable: false,
    type: 'text',
  },
  {
    id: 'accessInstructions',
    label: '',
    value: '',
    editable: false,
    type: 'text',
  },
];

export const UserDetailsForm: React.FC<UserDetailsFormProps> = ({ open, onClose, onSave }) => {
  const { user, isLoaded } = useUser();
  const [fields, setFields] = React.useState<PersonalizationCardField[]>(initialFields);
  const [addressValue, setAddressValue] = React.useState<string | AddressFields>({
    line1: '',
    line2: '',
    city: '',
    postcode: '',
  });
  const [accessInstructionsValue, setAccessInstructionsValue] = React.useState<string>('');
  const [emailValue, setEmailValue] = React.useState<string>('');
  const [phoneValue, setPhoneValue] = React.useState<string>('');
  const [isEmailValid, setIsEmailValid] = React.useState<boolean>(false);
  const [isPhoneValid, setIsPhoneValid] = React.useState<boolean>(false);

  // Check verification status from Clerk
  const isEmailVerified =
    isLoaded && user?.emailAddresses?.[0]?.verification?.status === 'verified';
  const isPhoneVerified = isLoaded && user?.phoneNumbers?.[0]?.verification?.status === 'verified';

  const handleFieldChange = (id: string, value: any) => {
    if (id === 'address') {
      setAddressValue(value);
      // Also update the field for data collection
      setFields((prev) =>
        prev.map((f) => {
          if (f.id === id) {
            if (typeof value === 'object' && value !== null) {
              const addressValue = value as AddressFields;
              const formattedAddress = [
                addressValue.line1,
                addressValue.line2,
                addressValue.city,
                addressValue.postcode,
              ]
                .filter(Boolean)
                .join(', ');
              return { ...f, value: formattedAddress };
            }
            return { ...f, value };
          }
          return f;
        })
      );
    } else if (id === 'accessInstructions') {
      setAccessInstructionsValue(value as string);
      // Also update the field for data collection
      setFields((prev) => prev.map((f) => (f.id === id ? { ...f, value } : f)));
    } else {
      setFields((prev) => prev.map((f) => (f.id === id ? { ...f, value } : f)));
    }
  };

  // Check if all required fields are filled
  const isFormComplete = () => {
    // Check name field
    const nameField = fields.find((f) => f.id === 'name');
    const hasName = nameField?.value && String(nameField.value).trim() !== '';

    // Check email field (must have value)
    const hasEmail = emailValue && emailValue.trim() !== '';

    // Check phone field (must have value)
    const hasPhone = phoneValue && phoneValue.trim() !== '';

    // Check address field (must have value)
    const hasAddress =
      addressValue &&
      (typeof addressValue === 'string'
        ? addressValue.trim() !== ''
        : Object.values(addressValue).some((val) => val && val.trim() !== ''));

    return hasName && hasEmail && hasPhone && hasAddress;
  };

  const handleSave = () => {
    const data: Record<string, any> = {};
    fields.forEach((f) => {
      if (f.id === 'address') {
        data[f.id] = addressValue;
      } else if (f.id === 'accessInstructions') {
        data[f.id] = accessInstructionsValue;
      } else {
        data[f.id] = f.value;
      }
    });
    onSave?.(data);
  };

  return (
    <Modal
      open={open}
      hideClose
      onClose={() => {}}
      title=""
      actionButtons={
        <Button
          type={ButtonType.PRIMARY}
          color={ButtonColor.GREEN_PRIMARY}
          size={ButtonSize.L}
          state={isFormComplete() ? ButtonState.DEFAULT : ButtonState.DISABLED}
          onClick={handleSave}
          className={styles.saveButton}
        >
          Next
        </Button>
      }
    >
      <div className={styles.container}>
        <PersonalizationCard
          fields={fields}
          onSave={handleSave}
          onEdit={() => {}}
          onCancel={onClose}
          title="Your details"
          titleEditable={false}
          transparent
          hideActions
          className={`${styles.personalizationCardTitle} ${styles.noTableMargin} ${styles.elementSpacing}`}
          renderAfterField={(field) => {
            if (field.id === 'name') {
              return <div className={styles.elementSpacing}></div>;
            }
            if (field.id === 'email') {
              return (
                <div>
                  <MultiFieldInput
                    type={MultiFieldInputType.EMAIL}
                    title="Email Address"
                    value={emailValue}
                    onChange={(val) => {
                      setEmailValue(val as string);
                      handleFieldChange('email', val);
                    }}
                    onValidationChange={setIsEmailValid}
                    placeholder="Enter email address"
                    hideBorder
                    hideSaveButton
                    isVerified={isEmailVerified}
                    className={styles.noPadding}
                  />
                  <div className={styles.elementSpacing} style={{ marginTop: 8 }}>
                    <Button
                      type={ButtonType.PRIMARY}
                      color={ButtonColor.GREEN_PRIMARY}
                      size={ButtonSize.L}
                      state={!isEmailValid ? ButtonState.DISABLED : ButtonState.DEFAULT}
                      onClick={() => {}}
                    >
                      Verify email
                    </Button>
                  </div>
                </div>
              );
            }
            if (field.id === 'phone') {
              return (
                <div>
                  <MultiFieldInput
                    type={MultiFieldInputType.PHONE}
                    title="Phone number"
                    value={phoneValue}
                    onChange={(val) => {
                      setPhoneValue(val as string);
                      handleFieldChange('phone', val);
                    }}
                    onValidationChange={setIsPhoneValid}
                    placeholder="Enter phone number"
                    hideBorder
                    hideSaveButton
                    isVerified={isPhoneVerified}
                    className={styles.noPadding}
                  />
                  <div className={styles.elementSpacing} style={{ marginTop: 8 }}>
                    <Button
                      type={ButtonType.PRIMARY}
                      color={ButtonColor.GREEN_PRIMARY}
                      size={ButtonSize.L}
                      state={!isPhoneValid ? ButtonState.DISABLED : ButtonState.DEFAULT}
                      onClick={() => {}}
                    >
                      Verify phone number
                    </Button>
                  </div>
                </div>
              );
            }
            if (field.id === 'address') {
              return (
                <div className={styles.elementSpacing}>
                  <MultiFieldInput
                    type={MultiFieldInputType.ADDRESS}
                    title="Address"
                    value={addressValue}
                    onChange={(val) => handleFieldChange('address', val)}
                    placeholder="Find address"
                    manualEntryText="Enter address manually"
                    hideBorder
                    hideSaveButton
                    className={styles.noPadding}
                  />
                </div>
              );
            }
            if (field.id === 'accessInstructions') {
              return (
                <div>
                  <MultiFieldInput
                    type={MultiFieldInputType.ACCESS_INSTRUCTION}
                    value={accessInstructionsValue}
                    onChange={(val) => handleFieldChange('accessInstructions', val)}
                    placeholder="Placeholder text"
                    title="Access/Parking instructions"
                    hideBorder
                    hideSaveButton
                    className={styles.noPadding}
                  />
                  <div className={styles.helperText}>Placeholder explainer copy goes here</div>
                </div>
              );
            }
            return null;
          }}
        />
      </div>
    </Modal>
  );
};
