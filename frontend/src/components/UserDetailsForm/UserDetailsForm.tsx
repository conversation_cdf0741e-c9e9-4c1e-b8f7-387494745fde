import React from 'react';
import { Modal } from '@/components/Modal';
import { PersonalizationCard } from '@/components/PersonalizationCard';
import { MultiFieldInput, MultiFieldInputType, AddressFields } from '@/components/MultiFieldInput';
import { Button } from '@/components/Button';
import { ButtonType, ButtonColor, ButtonSize } from '@/components/Button/Button.types';
import styles from './UserDetailsForm.module.scss';

import { PersonalizationCardField } from '@/components/PersonalizationCard/PersonalizationCard.types';

interface UserDetailsFormProps {
  open: boolean;
  onClose: () => void;
  onSave?: (data: Record<string, any>) => void;
}

const initialFields: PersonalizationCardField[] = [
  { id: 'name', label: 'Your name', value: '', editable: true, type: 'text' },
  { id: 'email', label: 'Email Address', value: '', editable: true, type: 'text' },
  { id: 'phone', label: 'Phone number', value: '', editable: true, type: 'phone' },
  {
    id: 'accessInstructions',
    label: '',
    value: '',
    editable: false,
    type: 'text',
  },
  {
    id: 'address',
    label: '',
    value: '',
    editable: false,
    type: 'text',
  },
];

export const UserDetailsForm: React.FC<UserDetailsFormProps> = ({ open, onClose, onSave }) => {
  const [fields, setFields] = React.useState<PersonalizationCardField[]>(initialFields);

  const handleFieldChange = (id: string, value: any) => {
    setFields((prev) =>
      prev.map((f) => {
        if (f.id === id) {
          if (id === 'address' && typeof value === 'object' && value !== null) {
            const addressValue = value as AddressFields;
            const formattedAddress = [
              addressValue.line1,
              addressValue.line2,
              addressValue.city,
              addressValue.postcode,
            ]
              .filter(Boolean)
              .join(', ');
            return { ...f, value: formattedAddress };
          }
          return { ...f, value };
        }
        return f;
      })
    );
  };

  const handleSave = () => {
    const data: Record<string, any> = {};
    fields.forEach((f) => {
      data[f.id] = f.value;
    });
    onSave?.(data);
  };

  return (
    <Modal open={open} hideClose onClose={() => {}}>
      <div className={styles.container}>
        <PersonalizationCard
          fields={fields}
          onSave={handleSave}
          onEdit={() => {}}
          onCancel={onClose}
          title="Your details"
          titleEditable={false}
          transparent
          hideActions
          className={styles.personalizationCardTitle}
          renderAfterField={(field) => {
            if (field.id === 'phone') {
              return (
                <div style={{ marginTop: 8 }}>
                  <Button
                    type={ButtonType.PRIMARY}
                    color={ButtonColor.GREEN_PRIMARY}
                    size={ButtonSize.L}
                    onClick={() => {}}
                  >
                    Verify phone number
                  </Button>
                </div>
              );
            }
            if (field.id === 'address') {
              const addressValue =
                typeof field.value === 'string' && field.value
                  ? field.value
                  : { line1: '', line2: '', city: '', postcode: '' };

              return (
                <MultiFieldInput
                  type={MultiFieldInputType.ADDRESS}
                  title="Address"
                  value={addressValue}
                  onChange={(val) => handleFieldChange('address', val)}
                  placeholder="Find address"
                  manualEntryText="Enter address manually"
                  hideBorder
                  hideSaveButton
                  className={`${styles.noPadding} ${styles.multiFieldTitle}`}
                />
              );
            }
            if (field.id === 'accessInstructions') {
              return (
                <MultiFieldInput
                  type={MultiFieldInputType.TEXTAREA}
                  value={field.value as string}
                  onChange={(val) => handleFieldChange('accessInstructions', val)}
                  placeholder="Placeholder text"
                  isTextarea
                  title="Access/Parking instructions"
                  hideBorder
                  className={`${styles.noPadding} ${styles.multiFieldTitle}`}
                />
              );
            }
            return null;
          }}
        />
        <div className={styles.section}>
          <Button
            type={ButtonType.PRIMARY}
            color={ButtonColor.GREEN_PRIMARY}
            size={ButtonSize.L}
            onClick={handleSave}
            className={styles.saveButton}
          >
            Next
          </Button>
        </div>
      </div>
    </Modal>
  );
};
