import React from 'react';
import { useUser } from '@clerk/nextjs';
import { Modal } from '@/components/Modal';
import { PersonalizationCard } from '@/components/PersonalizationCard';
import { MultiFieldInput, MultiFieldInputType, AddressFields } from '@/components/MultiFieldInput';
import { Button } from '@/components/Button';
import { ButtonType, ButtonColor, ButtonSize, ButtonState } from '@/components/Button/Button.types';
import styles from './UserDetailsForm.module.scss';

import { PersonalizationCardField } from '@/components/PersonalizationCard/PersonalizationCard.types';

interface UserDetailsFormProps {
  open: boolean;
  onClose: () => void;
  onSave?: (data: Record<string, any>) => void;
}

const initialFields: PersonalizationCardField[] = [
  { id: 'name', label: 'Your name', value: '', editable: true, type: 'text' },
  { id: 'email', label: 'Email Address', value: '', editable: true, type: 'text' },
  { id: 'phone', label: 'Phone number', value: '', editable: true, type: 'text' },
  { id: 'address', label: 'Address', value: '', editable: true, type: 'text' },
  {
    id: 'accessInstructions',
    label: 'Access/Parking instructions',
    value: '',
    editable: true,
    type: 'text',
  },
];

export const UserDetailsForm: React.FC<UserDetailsFormProps> = ({ open, onClose, onSave }) => {
  const { user, isLoaded } = useUser();
  const [fields, setFields] = React.useState<PersonalizationCardField[]>(initialFields);
  const [isEmailValid, setIsEmailValid] = React.useState<boolean>(false);
  const [isPhoneValid, setIsPhoneValid] = React.useState<boolean>(false);

  // Check verification status from Clerk
  const isEmailVerified =
    isLoaded && user?.emailAddresses?.[0]?.verification?.status === 'verified';
  const isPhoneVerified = isLoaded && user?.phoneNumbers?.[0]?.verification?.status === 'verified';

  // Handle field changes from PersonalizationCard
  const handleFieldChange = (id: string, value: any) => {
    setFields((prev) => prev.map((f) => (f.id === id ? { ...f, value } : f)));
  };

  // Check if all required fields are filled
  const isFormComplete = () => {
    const nameField = fields.find((f) => f.id === 'name');
    const emailField = fields.find((f) => f.id === 'email');
    const phoneField = fields.find((f) => f.id === 'phone');
    const addressField = fields.find((f) => f.id === 'address');

    const hasName = nameField?.value && String(nameField.value).trim() !== '';
    const hasEmail = emailField?.value && String(emailField.value).trim() !== '';
    const hasPhone = phoneField?.value && String(phoneField.value).trim() !== '';
    const hasAddress = addressField?.value && String(addressField.value).trim() !== '';

    console.log('Form completion check:', {
      hasName,
      hasEmail,
      hasPhone,
      hasAddress,
      nameValue: nameField?.value,
      emailValue: emailField?.value,
      phoneValue: phoneField?.value,
      addressValue: addressField?.value,
    });

    return hasName && hasEmail && hasPhone && hasAddress;
  };

  const handleSave = () => {
    const data: Record<string, any> = {};
    fields.forEach((f) => {
      data[f.id] = f.value;
    });
    onSave?.(data);
  };

  return (
    <Modal
      open={open}
      hideClose
      onClose={() => {}}
      title=""
      actionButtons={
        <Button
          type={ButtonType.PRIMARY}
          color={ButtonColor.GREEN_PRIMARY}
          size={ButtonSize.L}
          state={isFormComplete() ? ButtonState.DEFAULT : ButtonState.DISABLED}
          onClick={handleSave}
          className={styles.saveButton}
        >
          Next
        </Button>
      }
    >
      <div className={styles.container}>
        <PersonalizationCard
          fields={fields}
          onSave={handleSave}
          onEdit={() => {}}
          onCancel={onClose}
          title="Your details"
          titleEditable={false}
          transparent
          hideActions
          className={`${styles.personalizationCardTitle} ${styles.noTableMargin} ${styles.elementSpacing}`}
          renderAfterField={(field) => {
            if (field.id === 'name') {
              return (
                <div>
                  <MultiFieldInput
                    type={MultiFieldInputType.TEXT}
                    title="Your name"
                    value={nameValue}
                    onChange={(val) => {
                      setNameValue(val as string);
                      handleFieldChange('name', val);
                    }}
                    placeholder="Enter your name"
                    hideBorder
                    hideSaveButton
                    className={styles.noPadding}
                  />
                  <div className={styles.elementSpacing}></div>
                </div>
              );
            }
            if (field.id === 'email') {
              return (
                <div>
                  <MultiFieldInput
                    type={MultiFieldInputType.EMAIL}
                    title="Email Address"
                    value={emailValue}
                    onChange={(val) => {
                      setEmailValue(val as string);
                      handleFieldChange('email', val);
                    }}
                    onValidationChange={setIsEmailValid}
                    placeholder="Enter email address"
                    hideBorder
                    hideSaveButton
                    isVerified={isEmailVerified}
                    className={styles.noPadding}
                  />
                  <div className={styles.elementSpacing} style={{ marginTop: 8 }}>
                    <Button
                      type={ButtonType.PRIMARY}
                      color={ButtonColor.GREEN_PRIMARY}
                      size={ButtonSize.L}
                      state={!isEmailValid ? ButtonState.DISABLED : ButtonState.DEFAULT}
                      onClick={() => {}}
                    >
                      Verify email
                    </Button>
                  </div>
                </div>
              );
            }
            if (field.id === 'phone') {
              return (
                <div>
                  <MultiFieldInput
                    type={MultiFieldInputType.PHONE}
                    title="Phone number"
                    value={phoneValue}
                    onChange={(val) => {
                      setPhoneValue(val as string);
                      handleFieldChange('phone', val);
                    }}
                    onValidationChange={setIsPhoneValid}
                    placeholder="Enter phone number"
                    hideBorder
                    hideSaveButton
                    isVerified={isPhoneVerified}
                    className={styles.noPadding}
                  />
                  <div className={styles.elementSpacing} style={{ marginTop: 8 }}>
                    <Button
                      type={ButtonType.PRIMARY}
                      color={ButtonColor.GREEN_PRIMARY}
                      size={ButtonSize.L}
                      state={!isPhoneValid ? ButtonState.DISABLED : ButtonState.DEFAULT}
                      onClick={() => {}}
                    >
                      Verify phone number
                    </Button>
                  </div>
                </div>
              );
            }
            if (field.id === 'address') {
              return (
                <div className={styles.elementSpacing}>
                  <MultiFieldInput
                    type={MultiFieldInputType.ADDRESS}
                    title="Address"
                    value={addressValue}
                    onChange={(val) => handleFieldChange('address', val)}
                    placeholder="Find address"
                    manualEntryText="Enter address manually"
                    hideBorder
                    hideSaveButton
                    className={styles.noPadding}
                  />
                </div>
              );
            }
            if (field.id === 'accessInstructions') {
              return (
                <div>
                  <MultiFieldInput
                    type={MultiFieldInputType.ACCESS_INSTRUCTION}
                    value={accessInstructionsValue}
                    onChange={(val) => handleFieldChange('accessInstructions', val)}
                    placeholder="Placeholder text"
                    title="Access/Parking instructions"
                    hideBorder
                    hideSaveButton
                    className={styles.noPadding}
                  />
                  <div className={styles.helperText}>Placeholder explainer copy goes here</div>
                </div>
              );
            }
            return null;
          }}
        />
      </div>
    </Modal>
  );
};
