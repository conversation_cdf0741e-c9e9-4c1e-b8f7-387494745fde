.container {
	width: 100%;
	display: flex;
	flex-direction: column;
	margin-top: 50px;
}

.section {
	display: flex;
	flex-direction: column;
	gap: 8px;
	margin-bottom: 12px;
}

.helperText {
	font-size: 14px;
	margin-top: 4px;
}

.saveButton {
	width: 100%;
	margin-top: 12px;
}

.title {
	font-size: 2rem;
	font-weight: 700;
	color: #2d2926;
	margin-top: 0;
	line-height: 1.2;
}

.phoneField,
.accessInstructionsField {
	width: 100%;
	max-width: 100%;
	display: block;
	height: 70px;
}

.noPadding {
	padding: 8px 0 0 0;
	min-height: 0;
}

.personalizationCardTitle {
	:global(.title) {
		font-weight: bold !important;
		font-size: 26px !important;
	}
}