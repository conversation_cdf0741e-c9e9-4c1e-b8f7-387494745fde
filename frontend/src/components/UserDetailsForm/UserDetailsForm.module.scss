.container {
	width: 100%;
	display: flex;
	flex-direction: column;
	margin-top: 50px;
	gap: 8px;
}

.section {
	display: flex;
	flex-direction: column;
}

.helperText {
	font-size: 14px;
	color: var(--colors-gray-500)
}

.saveButton {
	width: 100%;
	margin-top: 12px;
}

.title {
	font-size: 2rem;
	font-weight: 700;
	color: #2d2926;
	margin-top: 0;
	line-height: 1.2;
}

.phoneField,
.accessInstructionsField {
	width: 100%;
	max-width: 100%;
	display: block;
	height: 70px;
}

.noPadding {
	padding: 0;
	min-height: 0;
}

.personalizationCardTitle {
	table td:first-child {
		font-weight: bold;
		color: var(--colors-gray-900);
	}
}

.noTableMargin {
	:global(table) {
		margin-bottom: 0 !important;
	}

	:global(.table) {
		margin-bottom: 0 !important;
	}

	:global(div table) {
		margin-bottom: 0 !important;
	}

	:global([class*="table"]) {
		margin-bottom: 0 !important;
	}
}