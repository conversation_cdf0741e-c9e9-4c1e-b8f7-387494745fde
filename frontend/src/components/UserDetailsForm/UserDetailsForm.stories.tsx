import React, { useState, useEffect } from 'react';
import { UserDetailsForm } from './UserDetailsForm';
import type { Meta, StoryObj } from '@storybook/react';

// Mock Clerk user with verified email and phone
const mockVerifiedUser = {
  id: 'user_123',
  firstName: 'John',
  lastName: 'Doe',
  emailAddresses: [
    {
      id: 'email_123',
      emailAddress: '<EMAIL>',
      verification: { status: 'verified' },
    },
  ],
  phoneNumbers: [
    {
      id: 'phone_123',
      phoneNumber: '+44 7700 900123',
      verification: { status: 'verified' },
    },
  ],
};

// Mock useUser hook
const mockUseUser = (isVerified = false) => ({
  user: isVerified ? mockVerifiedUser : null,
  isLoaded: true,
});

const meta: Meta<typeof UserDetailsForm> = {
  title: 'Components/UserDetailsForm',
  component: UserDetailsForm,
  parameters: {
    layout: 'centered',
  },
  argTypes: {
    open: { control: 'boolean' },
    onClose: { action: 'onClose' },
    onSave: { action: 'onSave' },
  },
};
export default meta;

type Story = StoryObj<typeof UserDetailsForm>;

export const Default: Story = {
  args: {
    open: true,
  },
  parameters: {
    docs: {
      description: {
        story:
          'Default state of the form. The "Next" button is disabled until all required fields are filled: Name, Email, Phone, and Address. Access/Parking instructions is optional.',
      },
    },
  },
};

const ValidatedFieldsWrapper = (args: any) => {
  const [isOpen, setIsOpen] = useState(true);

  useEffect(() => {
    // Simulate filling in valid email and phone after component mounts
    const timer = setTimeout(() => {
      // Find email input and fill it with valid email
      const emailInputs = document.querySelectorAll('input[type="text"]');
      const phoneInputs = document.querySelectorAll('input[type="tel"]');

      emailInputs.forEach((input) => {
        const htmlInput = input as HTMLInputElement;
        if (htmlInput.placeholder?.toLowerCase().includes('email')) {
          htmlInput.value = '<EMAIL>';
          htmlInput.dispatchEvent(new Event('input', { bubbles: true }));
          htmlInput.dispatchEvent(new Event('change', { bubbles: true }));
        }
      });

      phoneInputs.forEach((input) => {
        const htmlInput = input as HTMLInputElement;
        htmlInput.value = '+44 7700 900123';
        htmlInput.dispatchEvent(new Event('input', { bubbles: true }));
        htmlInput.dispatchEvent(new Event('change', { bubbles: true }));
        // Force React to update
        const nativeInputValueSetter = Object.getOwnPropertyDescriptor(
          window.HTMLInputElement.prototype,
          'value'
        )?.set;
        if (nativeInputValueSetter) {
          nativeInputValueSetter.call(htmlInput, '+44 7700 900123');
          htmlInput.dispatchEvent(new Event('input', { bubbles: true }));
        }
      });

      // Alternative approach: find inputs by their parent MultiFieldInput context
      const phoneTitle = Array.from(document.querySelectorAll('div')).find(
        (div) => div.textContent?.trim() === 'Phone number'
      );
      if (phoneTitle) {
        const phoneContainer = phoneTitle.closest('[class*="container"]');
        const phoneInput = phoneContainer?.querySelector('input[type="tel"]') as HTMLInputElement;
        if (phoneInput) {
          phoneInput.value = '+44 7700 900123';
          phoneInput.dispatchEvent(new Event('input', { bubbles: true }));
          phoneInput.dispatchEvent(new Event('change', { bubbles: true }));
        }
      }

      const emailTitle = Array.from(document.querySelectorAll('div')).find(
        (div) => div.textContent?.trim() === 'Email Address'
      );
      if (emailTitle) {
        const emailContainer = emailTitle.closest('[class*="container"]');
        const emailInput = emailContainer?.querySelector('input[type="text"]') as HTMLInputElement;
        if (emailInput) {
          emailInput.value = '<EMAIL>';
          emailInput.dispatchEvent(new Event('input', { bubbles: true }));
          emailInput.dispatchEvent(new Event('change', { bubbles: true }));
        }
      }

      // Fill name field
      const nameInput = document.querySelector('input[placeholder*="name" i]') as HTMLInputElement;
      if (nameInput) {
        nameInput.value = 'John Doe';
        nameInput.dispatchEvent(new Event('input', { bubbles: true }));
        nameInput.dispatchEvent(new Event('change', { bubbles: true }));
      }
    }, 500);

    return () => clearTimeout(timer);
  }, [isOpen]);

  return (
    <UserDetailsForm
      {...args}
      open={isOpen}
      onClose={() => setIsOpen(false)}
      onSave={(data) => {
        console.log('Saved validated data:', data);
        setIsOpen(false);
      }}
    />
  );
};

export const WithValidatedFields: Story = {
  args: {
    open: true,
  },
  render: ValidatedFieldsWrapper,
  parameters: {
    docs: {
      description: {
        story:
          'Shows the form with pre-filled valid email and phone number, displaying validation checkmark icons. Email and phone fields are automatically filled after 500ms to demonstrate the validation state. The "Next" button becomes active once all required fields are filled.',
      },
    },
  },
};

// Component wrapper with verified Clerk user
const VerifiedUserWrapper = (args: any) => {
  const [isOpen, setIsOpen] = useState(true);

  // Mock the useUser hook to return verified user
  React.useEffect(() => {
    // This is a simple mock for Storybook demonstration
    // In real implementation, this would come from Clerk
    const originalModule = require('@clerk/nextjs');
    if (originalModule.useUser) {
      originalModule.useUser = () => mockUseUser(true);
    }
  }, []);

  return (
    <UserDetailsForm
      {...args}
      open={isOpen}
      onClose={() => setIsOpen(false)}
      onSave={(data) => {
        console.log('Saved verified user data:', data);
        setIsOpen(false);
      }}
    />
  );
};

export const WithVerifiedFields: Story = {
  args: {
    open: true,
  },
  render: VerifiedUserWrapper,
  parameters: {
    docs: {
      description: {
        story:
          "Shows the form with verified email and phone number from Clerk profile. Green checkmark icons indicate fields that are verified in the user's Clerk profile. The form displays pre-filled values: <EMAIL> (verified) and +44 7700 900123 (verified).",
      },
    },
  },
};

export const FormValidation: Story = {
  args: {
    open: true,
  },
  parameters: {
    docs: {
      description: {
        story: `Interactive demo showing form validation and Next button behavior:

**Required Fields (must be filled for Next button to be active):**
- Name: Enter any name
- Email Address: Enter a valid email
- Phone number: Enter a valid UK phone number
- Address: Enter or select an address

**Optional Field:**
- Access/Parking instructions: Can be left empty

**Next Button Behavior:**
- Disabled (gray) when any required field is empty
- Active (green) when all required fields are filled
- Access instructions field does not affect button state

Try filling the fields one by one and watch the Next button become active!`,
      },
    },
  },
};
