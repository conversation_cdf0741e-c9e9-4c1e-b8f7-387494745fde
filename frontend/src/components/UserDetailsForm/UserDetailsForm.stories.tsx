import React, { useState, useEffect } from 'react';
import { UserDetailsForm } from './UserDetailsForm';
import type { Meta, StoryObj } from '@storybook/react';

const meta: Meta<typeof UserDetailsForm> = {
  title: 'Components/UserDetailsForm',
  component: UserDetailsForm,
  parameters: {
    layout: 'centered',
  },
  argTypes: {
    open: { control: 'boolean' },
    onClose: { action: 'onClose' },
    onSave: { action: 'onSave' },
  },
};
export default meta;

type Story = StoryObj<typeof UserDetailsForm>;

export const Default: Story = {
  args: {
    open: true,
  },
};

export const Filled: Story = {
  args: {
    open: true,
    onSave: (data) => console.log('Saved', data),
  },
  render: (args) => (
    <UserDetailsForm
      {...args}
      // @ts-ignore
      initialFields={[
        { id: 'name', label: 'Your name', value: '<PERSON>', editable: true, type: 'text' },
        {
          id: 'email',
          label: 'Email Address',
          value: '<EMAIL>',
          editable: true,
          type: 'text',
        },
        { id: 'address', label: 'Address', value: '10 Downing St', editable: true, type: 'text' },
      ]}
    />
  ),
};

export const Mobile: Story = {
  args: {
    open: true,
  },
  parameters: {
    viewport: {
      defaultViewport: 'mobile1',
    },
  },
};

export const WithError: Story = {
  args: {
    open: true,
  },
  render: (args) => <UserDetailsForm {...args} />,
};

// Component wrapper to demonstrate validated fields with icons
const ValidatedFieldsWrapper = (args: any) => {
  const [isOpen, setIsOpen] = useState(true);

  useEffect(() => {
    // Simulate filling in valid email and phone after component mounts
    const timer = setTimeout(() => {
      // Find email input and fill it with valid email
      const emailInputs = document.querySelectorAll('input[type="text"]');
      const phoneInputs = document.querySelectorAll('input[type="tel"]');

      emailInputs.forEach((input) => {
        const htmlInput = input as HTMLInputElement;
        if (htmlInput.placeholder?.toLowerCase().includes('email')) {
          htmlInput.value = '<EMAIL>';
          htmlInput.dispatchEvent(new Event('input', { bubbles: true }));
          htmlInput.dispatchEvent(new Event('change', { bubbles: true }));
        }
      });

      phoneInputs.forEach((input) => {
        const htmlInput = input as HTMLInputElement;
        if (htmlInput.placeholder?.toLowerCase().includes('phone')) {
          htmlInput.value = '+44 7700 900123';
          htmlInput.dispatchEvent(new Event('input', { bubbles: true }));
          htmlInput.dispatchEvent(new Event('change', { bubbles: true }));
        }
      });

      // Fill name field
      const nameInput = document.querySelector('input[placeholder*="name" i]') as HTMLInputElement;
      if (nameInput) {
        nameInput.value = 'John Doe';
        nameInput.dispatchEvent(new Event('input', { bubbles: true }));
        nameInput.dispatchEvent(new Event('change', { bubbles: true }));
      }

      // Fill address field
      const addressDiv = document.querySelector(
        '[data-placeholder*="address" i]'
      ) as HTMLDivElement;
      if (addressDiv) {
        addressDiv.textContent = '123 Main Street, London, SW1A 1AA';
        addressDiv.dispatchEvent(new Event('input', { bubbles: true }));
      }
    }, 500);

    return () => clearTimeout(timer);
  }, [isOpen]);

  return (
    <UserDetailsForm
      {...args}
      open={isOpen}
      onClose={() => setIsOpen(false)}
      onSave={(data) => {
        console.log('Saved validated data:', data);
        setIsOpen(false);
      }}
    />
  );
};

export const WithValidatedFields: Story = {
  args: {
    open: true,
  },
  render: ValidatedFieldsWrapper,
  parameters: {
    docs: {
      description: {
        story:
          'Shows the form with pre-filled valid email and phone number, displaying validation checkmark icons. Fields are automatically filled after 500ms to demonstrate the validation state.',
      },
    },
  },
};

export const ValidationDemo: Story = {
  args: {
    open: true,
  },
  parameters: {
    docs: {
      description: {
        story: `Interactive demo showing validation behavior:

**To see validation icons:**
- Enter a valid email (e.g., "<EMAIL>") in the Email Address field
- Enter a valid UK phone number (e.g., "+44 7700 900123" or "07700 900123") in the Phone number field
- Fill in the Address field with any text
- Green checkmark icons will appear on the right side of valid fields
- Verify buttons will become active only when fields are valid

**Validation Rules:**
- Email: Must be a valid email format (contains @ and domain)
- Phone: Must be a valid UK phone number format
- Address: Any non-empty text will show the checkmark`,
      },
    },
  },
};
