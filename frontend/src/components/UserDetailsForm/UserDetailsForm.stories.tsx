import React from 'react';
import { UserDetailsForm } from './UserDetailsForm';
import type { Meta, StoryObj } from '@storybook/react';

const meta: Meta<typeof UserDetailsForm> = {
  title: 'Components/UserDetailsForm',
  component: UserDetailsForm,
  parameters: {
    layout: 'centered',
  },
  argTypes: {
    open: { control: 'boolean' },
    onClose: { action: 'onClose' },
    onSave: { action: 'onSave' },
  },
};
export default meta;

type Story = StoryObj<typeof UserDetailsForm>;

export const Default: Story = {
  args: {
    open: true,
  },
};

export const Filled: Story = {
  args: {
    open: true,
    onSave: (data) => console.log('Saved', data),
  },
  render: (args) => (
    <UserDetailsForm
      {...args}
      // @ts-ignore
      initialFields={[
        { id: 'name', label: 'Your name', value: '<PERSON>', editable: true, type: 'text' },
        {
          id: 'email',
          label: 'Email Address',
          value: '<EMAIL>',
          editable: true,
          type: 'text',
        },
        { id: 'address', label: 'Address', value: '10 Downing St', editable: true, type: 'text' },
      ]}
    />
  ),
};

export const Mobile: Story = {
  args: {
    open: true,
  },
  parameters: {
    viewport: {
      defaultViewport: 'mobile1',
    },
  },
};

export const WithError: Story = {
  args: {
    open: true,
  },
  render: (args) => <UserDetailsForm {...args} />,
};
