import React, { useEffect, useState, useMemo } from 'react';
import { useAuth } from '@clerk/nextjs';
import { Spinner } from '@/components/Spinner/Spinner';
import styles from './MessageAttachments.module.scss';
import { HugeiconsIcon } from '@/components/HugeiconsIcon';
import * as StrokeRounded from '@hugeicons-pro/core-stroke-rounded';
import { IconSvgObject } from '@hugeicons/react';
import { MessageDocumentPreview } from './components';
import classNames from 'classnames';
import { Attachment } from '@/types/messages';
import { isImageFile } from '../TextMessage/TextMessage';
import { getApiUrl, API_ENDPOINTS } from '@/constants/api';

interface DocumentWithBlob {
  id: number;
  name: string;
  type: string;
  size: number;
  url: string;
  blob?: Blob;
  objectUrl?: string;
  error?: boolean;
  isImage?: boolean;
}

export interface FileAttachment {
  documentId: number;
  originalFileName?: string;
  fileExtension?: string;
  sizeInKiloBytes?: number;
  createdAt?: string;
  file?: File;
}

export interface MessageAttachmentsProps {
  isContentEmpty: boolean;
  attachments?: FileAttachment[];
  onDocumentsLoaded?: (data: {
    imageUploads: Attachment[];
    nonImageUploads: Attachment[];
    imageDocuments: DocumentWithBlob[];
    fileDocuments: DocumentWithBlob[];
  }) => void;
  renderNonImageUploads?: boolean;
}

const MessageImagePreview: React.FC<{ file: Attachment }> = ({ file }) => {
  const isImage = isImageFile(file);
  const filename = file.originalFileName || file.file?.name;

  return (
    <div className={styles.documentPreview}>
      {file.status !== 'success' && (
        <div className={styles.loaderContainer}>
          {file.status === 'uploading' && <Spinner color="#D28E28" />}
          {file.status === 'error' && (
            <HugeiconsIcon
              icon={StrokeRounded.AlertCircleStrokeRounded as unknown as IconSvgObject}
              size={24}
              color="#d32f2f"
            />
          )}
        </div>
      )}
      {file.status === 'success' && (
        <div className={styles.previewContent}>
          {isImage && file.cdnUrl && (
            <img src={file.cdnUrl} alt={filename} className={styles.previewImage} />
          )}
          {!isImage && <div className={styles.fileNameOnly}>{filename}</div>}
        </div>
      )}
    </div>
  );
};

export const NonImageUploads: React.FC<{
  uploads?: Attachment[];
}> = ({ uploads }) => {
  if (uploads?.length === 0) return null;

  return (
    <div className={styles.previewsContainer}>
      <div className={classNames(styles.itemsContainer, styles.nonImageUploads)}>
        {uploads?.map((fileUpload, index) => (
          <MessageDocumentPreview
            key={`${fileUpload.documentId}-${index}`}
            fileUpload={fileUpload}
            index={index}
          />
        ))}
      </div>
    </div>
  );
};

interface UseMessageAttachmentsResult {
  imageUploads: Attachment[];
  nonImageUploads: Attachment[];
  imageDocuments: DocumentWithBlob[];
  fileDocuments: DocumentWithBlob[];
  loading: boolean;
  error: string | null;
}

export const useMessageAttachments = (
  attachments?: FileAttachment[]
): UseMessageAttachmentsResult => {
  const { getToken } = useAuth();
  const [documents, setDocuments] = useState<DocumentWithBlob[]>([]);
  const [fileUploads, setFileUploads] = useState<Attachment[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const { imageUploads, nonImageUploads } = useMemo(() => {
    return fileUploads.reduce(
      (acc, fileUpload) => {
        const isImage = isImageFile(fileUpload);
        if (isImage) {
          acc.imageUploads.push(fileUpload);
        } else {
          acc.nonImageUploads.push(fileUpload);
        }
        return acc;
      },
      {
        imageUploads: [] as Attachment[],
        nonImageUploads: [] as Attachment[],
      }
    );
  }, [fileUploads]);

  const { imageDocuments, fileDocuments } = useMemo(() => {
    return documents.reduce(
      (acc, document) => {
        if (document.isImage) {
          acc.imageDocuments.push(document);
        } else {
          acc.fileDocuments.push(document);
        }
        return acc;
      },
      {
        imageDocuments: [] as DocumentWithBlob[],
        fileDocuments: [] as DocumentWithBlob[],
      }
    );
  }, [documents]);

  useEffect(() => {
    if (!attachments?.length) return;

    let isMounted = true;
    const abortController = new AbortController();
    const { signal } = abortController;

    const loadDocuments = async () => {
      setLoading(true);
      setError(null);

      try {
        const token = await getToken();
        if (!token || !isMounted) return;

        const imageAttachments: FileAttachment[] = [];
        const nonImageAttachments: FileAttachment[] = [];
        const isImageExtension = /^(jpg|jpeg|png|gif|bmp|webp|svg|tiff)$/i;

        attachments.forEach((attachment) => {
          if (attachment.fileExtension && isImageExtension.test(attachment.fileExtension)) {
            imageAttachments.push(attachment);
          } else {
            nonImageAttachments.push(attachment);
          }
        });

        const initialUploads: Attachment[] = [
          ...nonImageAttachments.map((attachment) => {
            const fileType = attachment.fileExtension
              ? `application/${attachment.fileExtension}`
              : 'application/octet-stream';
            const fileName = attachment.originalFileName || `Document ${attachment.documentId}`;

            return {
              documentId: attachment.documentId,
              name: fileName,
              type: fileType,
              size: attachment.sizeInKiloBytes ? attachment.sizeInKiloBytes * 1024 : undefined,
              fileId: attachment.documentId.toString(),
              file: new File([], fileName, { type: fileType }),
              status: 'success' as const,
              progress: 100,
              createdAt: attachment.createdAt,
              fileExtension: attachment.fileExtension,
              originalFileName: attachment.originalFileName,
              sizeInKiloBytes: attachment.sizeInKiloBytes,
            };
          }),

          ...imageAttachments.map((attachment) => {
            const fileName = attachment.originalFileName || `Document ${attachment.documentId}`;
            return {
              documentId: attachment.documentId,
              name: fileName,
              type: 'image',
              size: attachment.sizeInKiloBytes ? attachment.sizeInKiloBytes * 1024 : undefined,
              fileId: attachment.documentId.toString(),
              file: new File([], fileName),
              status: 'uploading' as const,
              progress: 0,
              createdAt: attachment.createdAt,
              fileExtension: attachment.fileExtension,
              originalFileName: attachment.originalFileName,
              sizeInKiloBytes: attachment.sizeInKiloBytes,
            };
          }),
        ];

        if (isMounted) {
          setFileUploads(initialUploads);
        }

        const nonImageDocuments = nonImageAttachments.map((attachment) => ({
          id: attachment.documentId,
          name: attachment.originalFileName || `Document ${attachment.documentId}`,
          type: attachment.fileExtension
            ? `application/${attachment.fileExtension}`
            : 'application/octet-stream',
          size: attachment.sizeInKiloBytes ? attachment.sizeInKiloBytes * 1024 : 0,
          url: getApiUrl(`${API_ENDPOINTS.DOCUMENTS}/${attachment.documentId}/`),
          isImage: false,
        }));

        const batchSize = 3; // Process 3 images at a time
        const validImageDocs: DocumentWithBlob[] = [];

        for (let i = 0; i < imageAttachments.length; i += batchSize) {
          if (!isMounted) break;

          const batch = imageAttachments.slice(i, i + batchSize);
          const batchPromises = batch.map(async (attachment) => {
            try {
              if (!isMounted) return null;

              if ('file' in attachment && attachment.file instanceof File) {
                const file = attachment.file;
                const contentType = file.type || '';
                const isImage =
                  contentType.startsWith('image/') ||
                  /^image\/(jpeg|png|gif|bmp|webp|svg\+xml|tiff)/.test(contentType);
                const filename = attachment.originalFileName || `Document ${attachment.documentId}`;

                const document: DocumentWithBlob = {
                  id: attachment.documentId,
                  name: filename,
                  type: contentType,
                  size: attachment.sizeInKiloBytes ? attachment.sizeInKiloBytes * 1024 : file.size,
                  url: URL.createObjectURL(file),
                  blob: file,
                  isImage,
                  objectUrl: isImage ? URL.createObjectURL(file) : undefined,
                };

                if (isMounted) {
                  setFileUploads((prev) =>
                    prev.map((upload) => {
                      if (upload.documentId === attachment.documentId) {
                        return {
                          ...upload,
                          status: 'success' as const,
                          progress: 100,
                          file: new File([file], filename, {
                            type: contentType,
                          }),
                          cdnUrl: document.url,
                        };
                      }
                      return upload;
                    })
                  );
                }

                return document;
              }

              const response = await fetch(
                getApiUrl(`${API_ENDPOINTS.DOCUMENTS}/${attachment.documentId}/`),
                {
                  headers: {
                    Authorization: `Bearer ${token}`,
                    Accept: '*/*',
                  },
                  signal, // Allow cancellation
                }
              );

              if (!response.ok) {
                throw new Error(`Failed to fetch document: ${response.status}`);
              }

              const contentType = response.headers.get('content-type') || '';
              const isImage =
                contentType.startsWith('image/') ||
                /^image\/(jpeg|png|gif|bmp|webp|svg\+xml|tiff)/.test(contentType);
              const blob = await response.blob();

              let filename = attachment.originalFileName || `Document ${attachment.documentId}`;
              if (!filename) {
                const disposition = response.headers.get('content-disposition');
                if (disposition?.includes('filename=')) {
                  const filenameMatch = disposition.match(/filename="(.+)"/);
                  if (filenameMatch?.[1]) {
                    filename = filenameMatch[1];
                  }
                } else if (contentType) {
                  const ext = contentType.split('/')[1]?.split(';')[0];
                  if (ext) filename = `${filename}.${ext}`;
                }
              }

              const document: DocumentWithBlob = {
                id: attachment.documentId,
                name: filename,
                type: contentType,
                size: attachment.sizeInKiloBytes ? attachment.sizeInKiloBytes * 1024 : blob.size,
                url: URL.createObjectURL(blob),
                blob,
                isImage,
                objectUrl: isImage ? URL.createObjectURL(blob) : undefined,
              };

              if (isMounted) {
                setFileUploads((prev) =>
                  prev.map((upload) => {
                    if (upload.documentId === attachment.documentId) {
                      return {
                        ...upload,
                        status: 'success' as const,
                        progress: 100,
                        file: new File([blob], filename, { type: contentType }),
                        cdnUrl: document.url,
                      };
                    }
                    return upload;
                  })
                );
              }

              return document;
            } catch (error) {
              if (isMounted) {
                setFileUploads((prev) =>
                  prev.map((upload) => {
                    if (upload.documentId === attachment.documentId) {
                      return {
                        ...upload,
                        status: 'error' as const,
                        progress: 0,
                      };
                    }
                    return upload;
                  })
                );
              }

              console.error('Error fetching document:', attachment.documentId, error);
              return null;
            }
          });

          const batchResults = await Promise.all(batchPromises);

          batchResults.forEach((doc) => {
            if (doc && !doc.error) validImageDocs.push(doc);
          });
        }

        if (isMounted) {
          setDocuments([...validImageDocs, ...nonImageDocuments]);
          setLoading(false);
        }
      } catch (err) {
        if (isMounted) {
          setError('Failed to load document attachments');
          setLoading(false);
          console.error('Error loading attachments:', err);
        }
      }
    };

    loadDocuments();

    return () => {
      isMounted = false;
      abortController.abort();

      documents.forEach((doc) => {
        if (doc.url?.startsWith('blob:')) {
          URL.revokeObjectURL(doc.url);
        }
        if (doc.objectUrl && doc.objectUrl !== doc.url && doc.objectUrl.startsWith('blob:')) {
          URL.revokeObjectURL(doc.objectUrl);
        }
      });
    };
  }, [attachments, getToken]);

  return {
    imageUploads,
    nonImageUploads,
    imageDocuments,
    fileDocuments,
    loading,
    error,
  };
};

export const MessageAttachments: React.FC<MessageAttachmentsProps> = ({
  isContentEmpty,
  attachments,
  onDocumentsLoaded,
  renderNonImageUploads = true,
}) => {
  const { imageUploads, nonImageUploads, imageDocuments, fileDocuments, loading, error } =
    useMessageAttachments(attachments);

  const hasNotifiedRef = React.useRef(false);

  useEffect(() => {
    if (
      onDocumentsLoaded &&
      (imageUploads.length > 0 || nonImageUploads.length > 0) &&
      !hasNotifiedRef.current
    ) {
      hasNotifiedRef.current = true;
      onDocumentsLoaded({
        imageUploads,
        nonImageUploads,
        imageDocuments,
        fileDocuments,
      });
    }
  }, [imageUploads, nonImageUploads, imageDocuments, fileDocuments, onDocumentsLoaded]);

  useEffect(() => {
    hasNotifiedRef.current = false;
  }, [attachments]);

  if (!attachments || attachments.length === 0) {
    return null;
  }

  if (loading && imageUploads.length === 0 && nonImageUploads.length === 0) {
    return <div className={styles.loading}>Loading attachments...</div>;
  }

  if (error && imageUploads.length === 0 && nonImageUploads.length === 0) {
    return <div className={styles.error}>{error}</div>;
  }

  return (
    <div
      className={classNames(styles.attachmentsContainer, {
        [styles.spacing]: isContentEmpty,
      })}
    >
      {imageUploads.length > 0 || nonImageUploads.length > 0 ? (
        <>
          {renderNonImageUploads && nonImageUploads.length > 0 && (
            <NonImageUploads uploads={nonImageUploads} />
          )}

          {imageUploads.length > 0 && (
            <div className={styles.previewsContainer}>
              <div className={styles.itemsContainer}>
                {imageUploads.map((fileUpload: Attachment, index: number) => (
                  <MessageImagePreview
                    key={`${fileUpload.documentId}-${index}`}
                    file={fileUpload}
                  />
                ))}
              </div>
            </div>
          )}
        </>
      ) : imageDocuments.length === 0 && fileDocuments.length === 0 ? (
        <div className={styles.error}>Unable to load attachments</div>
      ) : (
        <div className={styles.fallbackContainer}>
          {imageDocuments.length > 0 && (
            <div className={styles.imagesContainer}>
              <h4 className={styles.sectionTitle}>Images</h4>
              {imageDocuments.map((document: DocumentWithBlob) => (
                <div key={document.id} className={styles.imageContainer}>
                  <img
                    src={document.objectUrl}
                    alt={document.name}
                    className={styles.attachmentImage}
                  />
                  <div className={styles.imageName}>{document.name}</div>
                </div>
              ))}
            </div>
          )}

          {renderNonImageUploads && fileDocuments.length > 0 && (
            <div className={styles.filesContainer}>
              <h4 className={styles.sectionTitle}>Files</h4>
              {fileDocuments.map((document: DocumentWithBlob) => (
                <div key={document.id} className={styles.documentContainer}>
                  <HugeiconsIcon
                    icon={StrokeRounded.AiFileStrokeRounded as unknown as IconSvgObject}
                    size={24}
                    color="#666"
                  />
                  <a
                    href={document.url}
                    target="_blank"
                    rel="noopener noreferrer"
                    download={document.name}
                    className={styles.documentLink}
                  >
                    {document.name}
                  </a>
                </div>
              ))}
            </div>
          )}
        </div>
      )}
    </div>
  );
};
