import React, { useState, useRef, useEffect } from 'react';
import classNames from 'classnames';
import { imageExtensions, TextMessageProps } from './TextMessage.types';
import { LogoHA } from '../LogoHA';
import { extractButtons, stripButtonsFromText } from '@/utils/messageUtils';
import { QuickActions } from '@/components/QuickActions';
import { MarkdownRenderer } from '@/components/MarkdownRenderer/MarkdownRenderer';
import { Attachment } from '@/types/messages';
import { MessageAttachments, NonImageUploads } from '../MessageAttachments';
import { ClickableImages } from '../ClickableImages';
import { NonClickableImages } from '../NonClickableImages';
import { ConfirmToast } from '@/components/ConfirmToast';

import styles from './TextMessage.module.scss';
import { useMessages } from '@/hooks/useMessages';

export const isImageFile = (file: Attachment): boolean => {
  if (file.file?.type.startsWith('image/')) {
    return true;
  }

  if (file?.fileExtension) {
    const ext = file?.fileExtension.toLowerCase();
    return imageExtensions.includes(ext);
  }

  const extension = file.file?.name.split('.').pop()?.toLowerCase();
  if (extension) {
    return imageExtensions.includes(extension);
  }

  return false;
};

export const TextMessage: React.FC<TextMessageProps> = ({
  message,
  className,
  isAIThinking = false,
  messages,
  scrollToBottom,
  children,
}) => {
  const isUserMessage = message?.senderType === 'user';
  const isSystemMessage = message?.senderType === 'system' || isAIThinking;
  const [nonImageUploads, setNonImageUploads] = useState<Attachment[]>([]);
  const [imageUploads, setImageUploads] = useState<Attachment[]>([]);
  const [areImagesLoaded, setAreImagesLoaded] = useState(false);
  const wrapperRef = useRef<HTMLDivElement>(null);
  const { setMessageHeight } = useMessages();

  const buttons = message?.content ? extractButtons(message.content) : [];
  const cleanText = message?.content ? stripButtonsFromText(message.content) : '';

  const isDisabled = message?.senderType === 'system' && message?.id !== (messages || [])[0]?.id;

  const isLastMessage = message?.id === (messages || [])[0]?.id;
  const jobSummary = message?.additionalData?.jobSummary;

  useEffect(() => {
    if (wrapperRef?.current && areImagesLoaded) {
      if (message?.isOptimistic) {
        setMessageHeight({ optimistic: wrapperRef.current?.offsetHeight, lastSystem: 32 });
      } else if (isSystemMessage && isLastMessage) {
        setMessageHeight({ lastSystem: wrapperRef.current?.offsetHeight });
      }
    }
  }, [message?.isOptimistic, isSystemMessage, isLastMessage, setMessageHeight, areImagesLoaded]);

  const handleDocumentsLoaded = (data: {
    nonImageUploads: Attachment[];
    imageUploads: Attachment[];
  }) => {
    const uploads = message?.attachments || data.nonImageUploads;

    const strictlyNonImageUploads = uploads.filter((upload) => !isImageFile(upload));

    const misclassifiedImageUploads = uploads.filter((upload) => isImageFile(upload));

    setNonImageUploads(strictlyNonImageUploads);
    setImageUploads([...misclassifiedImageUploads]);
  };

  // If there are no clickable images to load, set areImagesLoaded to true
  useEffect(() => {
    if (
      !message?.additionalData?.imageClickableUrls ||
      message.additionalData?.imageClickableUrls.length === 0
    ) {
      setAreImagesLoaded(true);
    }
  }, [message?.additionalData?.imageClickableUrls]);

  const handleImagesLoaded = () => {
    setAreImagesLoaded(true);
  };

  return (
    <div className={styles.messageWrapper} data-testid="message-wrapper">
      <div
        ref={wrapperRef}
        className={classNames(styles.messageContainer, className, {
          [styles.userContainer]: isUserMessage,
          [styles.systemContainer]: isSystemMessage,
        })}
      >
        {isSystemMessage && (
          <div className={styles.logoContainer}>
            <LogoHA />
          </div>
        )}
        {nonImageUploads.length > 0 && <NonImageUploads uploads={nonImageUploads} />}
        <div className={styles.messageRow}>
          <div
            className={classNames(styles.bubble, {
              [styles.userBubble]: isUserMessage,
              [styles.systemBubble]: isSystemMessage,
              [styles.empty]: imageUploads?.length === 0 && !cleanText.trim(),
            })}
          >
            {isAIThinking ? (
              <div className={styles.loadingDots} />
            ) : (
              <div className={styles.messageContent}>
                <MessageAttachments
                  isContentEmpty={!cleanText.trim()}
                  attachments={message?.attachments}
                  onDocumentsLoaded={handleDocumentsLoaded}
                  renderNonImageUploads={false}
                />
                <div className={styles.content}>
                  <MarkdownRenderer content={cleanText} />
                </div>
                {children}
                <NonClickableImages message={message} />
                <ClickableImages
                  message={message}
                  isDisabled={isDisabled}
                  onImagesLoaded={handleImagesLoaded}
                />
                {buttons.length > 0 && !jobSummary && (
                  <div className={!isDisabled ? styles.buttonsContainer : styles.disabled}>
                    <QuickActions buttons={buttons} />
                  </div>
                )}
                {isLastMessage && jobSummary && (
                  <ConfirmToast
                    text="Once you confirm, your job details will be locked in. Alfie will take it from here and start reaching out for quotes."
                    isJobSummary={!!jobSummary}
                    isLastMessage={isLastMessage}
                    scrollToBottom={scrollToBottom}
                    message={message}
                  />
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};
