'use client';

import { useTheme } from 'next-themes';
import { Toaster as Sonner, ToasterProps, toast as sonnerToast } from 'sonner';
import { Toast, ToastColor } from '../Toast';

interface ToastProps {
  title: string;
}

const Toaster = ({ ...props }: ToasterProps) => {
  const { theme = 'system' } = useTheme();

  return (
    <Sonner
      theme={theme as ToasterProps['theme']}
      className="toaster group"
      style={
        {
          '--normal-bg': 'var(--popover)',
          '--normal-text': 'var(--popover-foreground)',
          '--normal-border': 'var(--border)',
          '--width': '766px',
          left: 'calc(50% + 130px)',
          transform: 'translateX(-50%)',
        } as React.CSSProperties
      }
      toastOptions={{
        style: {
          width: '100%',
        },
      }}
      {...props}
      duration={2000}
    />
  );
};

export const toast = {
  success: (toast: Omit<ToastProps, 'id'>) => {
    return sonnerToast.custom(() => <Toast color={ToastColor.SUCCESS} headingText={toast.title} />);
  },
  info: (toast: Omit<ToastProps, 'id'>) => {
    return sonnerToast.custom(() => <Toast color={ToastColor.INFO} headingText={toast.title} />);
  },
};

export { Toaster };
