import React, { ReactNode, useRef } from 'react';
import styles from './Modal.module.scss';
import { IconSvgObject } from '@hugeicons/react';
import * as StrokeStandard from '@hugeicons-pro/core-stroke-standard';
import { HugeiconsIcon } from '@/components/HugeiconsIcon';
import { useBreakpoint } from '@/utils/breakpointUtils';

interface ModalProps {
  open: boolean;
  onClose: () => void;
  children: ReactNode;
  actionButtons?: ReactNode;
  title?: string;
  hideClose?: boolean;
}

export const Modal: React.FC<ModalProps> = ({
  open,
  onClose,
  children,
  actionButtons,
  title,
  hideClose,
}) => {
  const contentRef = useRef<HTMLDivElement>(null);
  const { isMobile } = useBreakpoint();

  if (!open) return null;

  const handleOverlayClick = (e: React.MouseEvent<HTMLDivElement>) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  return (
    <div className={styles.overlay} onClick={handleOverlayClick}>
      <div
        className={isMobile ? `${styles.modal} ${styles.mobile}` : styles.modal}
        role="dialog"
        aria-modal="true"
      >
        <div className={styles.modalInner}>
          <div className={styles.header}>
            {title && <h2 className={styles.title}>{title}</h2>}
            {!hideClose && (
              <HugeiconsIcon
                icon={StrokeStandard.MultiplicationSignIcon as unknown as IconSvgObject}
                size={24}
                className={styles.closeButton}
                onClick={onClose}
              />
            )}
          </div>
          <div className={styles.content} ref={contentRef}>
            {children}
          </div>
        </div>
        {actionButtons && <div className={styles.actions}>{actionButtons}</div>}
      </div>
    </div>
  );
};
