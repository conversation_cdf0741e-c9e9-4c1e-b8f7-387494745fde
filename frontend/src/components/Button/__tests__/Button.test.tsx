import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { vi } from 'vitest';
import { Button } from '../Button';
import { ButtonColor, ButtonSize, ButtonState, ButtonType } from '../Button.types';

// Mock HugeIcons
vi.mock('@hugeicons/react', () => ({
  ArrowRight: ({ className, size }: { className: string; size: number }) => (
    <div data-testid="mock-icon" className={className}>
      Mock Icon {size}
    </div>
  ),
  ArrowLeft: ({ className, size }: { className: string; size: number }) => (
    <div data-testid="mock-icon" className={className}>
      Mock Icon {size}
    </div>
  ),
}));

describe('Button Component', () => {
  const defaultProps = {
    children: 'Test Button',
    type: ButtonType.PRIMARY,
    size: ButtonSize.BASE,
    color: ButtonColor.BLUE_SECONDARY,
    state: ButtonState.DEFAULT,
  };

  // Basic Rendering Tests
  describe('Rendering', () => {
    it('renders button with text correctly', () => {
      render(<Button {...defaultProps} />);
      expect(screen.getByText('Test Button')).toBeInTheDocument();
    });

    it('renders as link when href is provided', () => {
      render(<Button {...defaultProps} href="/test" />);
      const link = screen.getByRole('link');
      expect(link).toHaveAttribute('href', '/test');
    });

    it('applies disabled state correctly', () => {
      render(<Button {...defaultProps} state={ButtonState.DISABLED} />);
      expect(screen.getByRole('button')).toBeDisabled();
    });
  });

  // Icon Tests
  describe('Icons', () => {
    it('renders left icon when specified', () => {
      render(<Button {...defaultProps} leftIconName="ArrowLeft" showLeftIcon={true} />);
      expect(screen.getByTestId('mock-icon')).toBeInTheDocument();
    });

    it('renders right icon when specified', () => {
      render(<Button {...defaultProps} rightIconName="ArrowRight" showRightIcon={true} />);
      expect(screen.getByTestId('mock-icon')).toBeInTheDocument();
    });

    it('renders both icons when specified', () => {
      render(
        <Button
          {...defaultProps}
          leftIconName="ArrowLeft"
          rightIconName="ArrowRight"
          showLeftIcon={true}
          showRightIcon={true}
        />
      );
      const icons = screen.getAllByTestId('mock-icon');
      expect(icons).toHaveLength(2);
    });

    it('renders icon-only button correctly', () => {
      render(<Button {...defaultProps} leftIconName="ArrowLeft" iconOnly={true} />);
      expect(screen.getByTestId('mock-icon')).toBeInTheDocument();
      expect(screen.queryByText('Test Button')).not.toBeInTheDocument();
    });
  });

  // Variant Tests
  describe.skip('Variants', () => {
    it.each([
      [ButtonType.PRIMARY, '_primary_f8f296'],
      [ButtonType.SECONDARY, '_secondary_f8f296'],
      [ButtonType.TERTIARY, '_tertiary_f8f296'],
    ])('renders %s type correctly', (type, expectedClass) => {
      render(<Button {...defaultProps} type={type} />);
      const button = screen.getByRole('button');
      expect(button).toHaveClass(expectedClass);
    });

    it.each([
      [ButtonColor.BLUE_SECONDARY, '_blue-secondary_f8f296'],
      [ButtonColor.GREEN_PRIMARY, '_green-primary_f8f296'],
      [ButtonColor.RED, '_red_f8f296'],
      [ButtonColor.YELLOW, '_yellow_f8f296'],
    ])('renders %s color correctly', (color, expectedClass) => {
      render(<Button {...defaultProps} color={color} />);
      const button = screen.getByRole('button');
      expect(button).toHaveClass(expectedClass);
    });

    it.each([
      [ButtonSize.XS, '_xs_f8f296'],
      [ButtonSize.SM, '_sm_f8f296'],
      [ButtonSize.BASE, '_base_f8f296'],
      [ButtonSize.L, '_l_f8f296'],
      [ButtonSize.XL, '_xl_f8f296'],
    ])('renders %s size correctly', (size, expectedClass) => {
      render(<Button {...defaultProps} size={size} />);
      const button = screen.getByRole('button');
      expect(button).toHaveClass(expectedClass);
    });
  });

  // Interaction Tests
  describe('Interactions', () => {
    it('calls onClick handler when clicked', () => {
      const handleClick = vi.fn();
      render(<Button {...defaultProps} onClick={handleClick} />);
      fireEvent.click(screen.getByRole('button'));
      expect(handleClick).toHaveBeenCalledTimes(1);
    });

    it('does not call onClick when disabled', () => {
      const handleClick = vi.fn();
      render(<Button {...defaultProps} onClick={handleClick} state={ButtonState.DISABLED} />);
      fireEvent.click(screen.getByRole('button'));
      expect(handleClick).not.toHaveBeenCalled();
    });
  });

  // Class Composition Tests
  describe('Class Composition', () => {
    it('applies custom className correctly', () => {
      const customClass = 'custom-class';
      render(<Button {...defaultProps} className={customClass} />);
      expect(screen.getByRole('button')).toHaveClass(customClass);
    });

    it.skip('combines multiple variant classes correctly', () => {
      render(
        <Button
          {...defaultProps}
          type={ButtonType.PRIMARY}
          color={ButtonColor.GREEN_PRIMARY}
          size={ButtonSize.XL}
        />
      );
      const button = screen.getByRole('button');
      expect(button).toHaveClass('_primary_f8f296', '_green-primary_f8f296', '_xl_f8f296');
    });
  });

  // Edge Cases
  describe('Edge Cases', () => {
    it('handles missing icon names gracefully', () => {
      const { container } = render(
        <Button {...defaultProps} showLeftIcon={true} showRightIcon={true} />
      );
      const button = container.querySelector('button');
      expect(button).toBeInTheDocument();
    });

    it('handles empty button text', () => {
      const { container } = render(<Button {...defaultProps}></Button>);
      const button = container.querySelector('button');
      expect(button).toBeInTheDocument();
    });
  });
});
