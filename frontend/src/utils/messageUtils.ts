import { Attachment } from '../types/messages';

export interface MessageButton {
  text: string;
  value: string;
}

export const extractButtons = (content: string): MessageButton[] => {
  const buttons: MessageButton[] = [];

  const buttonRegex = /\[([^\]]+)\]\(([^)]+)\)/g;
  let match;
  while ((match = buttonRegex.exec(content)) !== null) {
    buttons.push({
      text: match[1],
      value: match[2],
    });
  }

  const optionsRegex = /\[Options:\s*([^\]]+)\]/g;
  while ((match = optionsRegex.exec(content)) !== null) {
    const options = match[1].split(';').map((opt) => opt.trim());
    options.forEach((option) => {
      buttons.push({
        text: option,
        value: option,
      });
    });
  }

  return buttons;
};

export const stripButtonsFromText = (content: string): string => {
  return content.replace(/\[Options:[^\]]+\]/g, '');
};

/**
 * Filters an array of image URLs to return only those that can be successfully loaded
 * @param imageUrls Array of image URLs to check
 * @returns Promise that resolves to an array of valid image URLs
 */
export const filterValidImages = async (imageUrls: string[]): Promise<string[]> => {
  if (!imageUrls || imageUrls.length === 0) {
    return [];
  }

  const checkImage = (url: string): Promise<string | null> => {
    return new Promise((resolve) => {
      const img = new Image();

      img.onload = () => {
        resolve(url);
      };

      img.onerror = () => {
        console.warn(`Image failed to load: ${url}`);
        resolve(null);
      };

      const timeoutId = setTimeout(() => {
        img.src = '';
        console.warn(`Image load timed out: ${url}`);
        resolve(null);
      }, 5000);

      img.src = url;

      img.onload = () => {
        clearTimeout(timeoutId);
        resolve(url);
      };
    });
  };

  const results = await Promise.all(imageUrls.map(checkImage));
  return results.filter((url): url is string => url !== null);
};

/**
 * Maps message documents to attachment format
 * @param documents Array of message documents
 * @returns Array of attachments in the expected format
 */
export const mapDocumentsToAttachments = (
  documents?: Attachment[] | Array<{
    id: number;
    fileName: string;
    sizeInKiloBytes: number;
    browserMimeType: string;
    createdAt: string;
    uploadContext: string;
    status: string;
    category?: string;
    label?: string;
    hasStatusBeenDisplayed?: boolean;
  }>
): Array<{
  documentId: number;
  name: string;
  type: string;
  status: 'uploading' | 'success' | 'error';
  originalFileName: string;
  sizeInKiloBytes: number;
  createdAt: string;
  fileExtension?: string;
  size?: number;
  progress?: number;
  fileId?: string;
  category?: string;
}> => {
  if (!documents) {
    return [];
  }

  return documents.map((doc) => {
    if ('documentId' in doc) {
      return {
        documentId: doc.documentId,
        name: doc.name,
        type: doc.type,
        status: doc.status as 'uploading' | 'success' | 'error',
        originalFileName: doc.originalFileName || doc.name,
        sizeInKiloBytes: doc.sizeInKiloBytes || 0,
        createdAt: doc.createdAt || new Date().toISOString(),
        fileExtension: doc.fileExtension,
        size: doc.size,
        progress: doc.progress,
        fileId: doc.fileId,
        category: doc.category,
      };
    }

    return {
      documentId: doc.id,
      name: doc.fileName,
      type: doc.browserMimeType || 'application/octet-stream',
      status: 'success' as const,
      originalFileName: doc.fileName,
      sizeInKiloBytes: doc.sizeInKiloBytes,
      createdAt: doc.createdAt,
      fileExtension: doc.browserMimeType?.split('/')[1],
      size: doc.sizeInKiloBytes ? doc.sizeInKiloBytes * 1024 : undefined,
      progress: 100,
      fileId: doc.id.toString(),
      category: doc.category,
    };
  });
};
