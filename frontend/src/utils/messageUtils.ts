export interface MessageButton {
  text: string;
  value: string;
}

export const extractButtons = (content: string): MessageButton[] => {
  const buttons: MessageButton[] = [];

  const buttonRegex = /\[([^\]]+)\]\(([^)]+)\)/g;
  let match;
  while ((match = buttonRegex.exec(content)) !== null) {
    buttons.push({
      text: match[1],
      value: match[2],
    });
  }

  const optionsRegex = /\[Options:\s*([^\]]+)\]/g;
  while ((match = optionsRegex.exec(content)) !== null) {
    const options = match[1].split(';').map((opt) => opt.trim());
    options.forEach((option) => {
      buttons.push({
        text: option,
        value: option,
      });
    });
  }

  return buttons;
};

export const stripButtonsFromText = (content: string): string => {
  return content.replace(/\[Options:[^\]]+\]/g, '');
};

/**
 * Filters an array of image URLs to return only those that can be successfully loaded
 * @param imageUrls Array of image URLs to check
 * @returns Promise that resolves to an array of valid image URLs
 */
export const filterValidImages = async (imageUrls: string[]): Promise<string[]> => {
  if (!imageUrls || imageUrls.length === 0) {
    return [];
  }

  const checkImage = (url: string): Promise<string | null> => {
    return new Promise((resolve) => {
      const img = new Image();

      img.onload = () => {
        resolve(url);
      };

      img.onerror = () => {
        console.warn(`Image failed to load: ${url}`);
        resolve(null);
      };

      const timeoutId = setTimeout(() => {
        img.src = '';
        console.warn(`Image load timed out: ${url}`);
        resolve(null);
      }, 5000);

      img.src = url;

      img.onload = () => {
        clearTimeout(timeoutId);
        resolve(url);
      };
    });
  };

  const results = await Promise.all(imageUrls.map(checkImage));
  return results.filter((url): url is string => url !== null);
};
