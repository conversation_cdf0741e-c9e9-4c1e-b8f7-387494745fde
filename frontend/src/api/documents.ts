import { API_ENDPOINTS, getApiUrl } from '@/constants/api';
import axios from 'axios';
import { Context } from '@/api/notifications';

export interface DocumentDto {
  id: number;
  fileName: string;
  sizeInKiloBytes: number;
  browserMimeType: string;
  createdAt: string;
  uploadContext: Context;
  status:
    | 'processing'
    | 'processingCompleted'
    | 'saved'
    | 'error'
    | 'warning'
    | 'info'
    | 'irrelevant';
  category?: string | null;
  label?: string | null;
  errorMessage?: string | null;
  hasStatusBeenDisplayed: boolean;
}

export function getUserFriendlyType(document: DocumentDto): string {
  return document.browserMimeType.split('/')?.at(1)?.toUpperCase() || 'FILE';
}

export function isImage(document: DocumentDto): boolean {
  return document.browserMimeType?.startsWith('image/') || false;
}

export interface DocumentListDto {
  items: DocumentDto[];
  total: number;
  page: number;
  size: number;
  pages: number;
}

export const fetchDocuments = async (
  token: string,
  uploadContext: Context | null,
  status?: DocumentDto['status']
): Promise<DocumentDto[]> => {
  try {
    let result: DocumentDto[] = [];
    for (let page = 1; page < 10; page++) {
      const params: { page: number; size: number; uploadContext: Context | null; status?: DocumentDto['status'] } = {
        page,
        size: 100,
        uploadContext
      };

      if (status) {
        params.status = status;
      }

      const response = await axios.get<DocumentListDto>(getApiUrl(`${API_ENDPOINTS.DOCUMENTS}/`), {
        params,
        headers: {
          Authorization: `Bearer ${token}`,
          Accept: 'application/json',
        },
      });

      if (!response.data) {
        throw new Error('Failed to fetch documents');
      }

      result = [...result, ...response.data.items];

      if (response.data.page >= response.data.pages) {
        break;
      }
    }
    return result;
  } catch (error) {
    console.error('Failed to fetch documents:', error);
    throw new Error('Network error during fetch');
  }
};

export async function fetchDocument(token: string, documentId: string | number): Promise<Blob> {
  try {
    const response = await axios.get<Blob>(getApiUrl(`${API_ENDPOINTS.DOCUMENTS}/${documentId}/`), {
      headers: {
        Authorization: `Bearer ${token}`,
        Accept: 'application/json',
      },
      responseType: 'blob',
    });

    if (!response.data) {
      throw new Error(`Failed to fetch document with ID: ${documentId}`);
    }

    return response.data;
  } catch (error) {
    console.error(`Failed to fetch document with ID: ${documentId}`, error);
    throw new Error('Network error during fetch');
  }
}

export async function deleteDocument(token: string, documentId: number): Promise<void> {
  try {
    await axios.delete(getApiUrl(`${API_ENDPOINTS.DOCUMENTS}/${documentId}/`), {
      headers: {
        Authorization: `Bearer ${token}`,
        Accept: 'application/json',
      },
    });
  } catch (error) {
    console.error('Delete document failed:', error);
    throw new Error('Failed to delete document');
  }
}

export async function uploadDocument(
  file: File,
  token: string,
  uploadContext: Context
): Promise<DocumentDto> {
  try {
    const formData = new FormData();
    formData.append('file', file);

    const response = await axios.post<DocumentDto>(
      getApiUrl(`${API_ENDPOINTS.DOCUMENTS}/`),
      formData,
      {
        params: { uploadContext },
        headers: {
          Authorization: `Bearer ${token}`,
          Accept: 'application/json',
        },
      }
    );

    if (!response.data) {
      throw new Error('Failed to upload document');
    }

    return response.data;
  } catch (error) {
    console.error('Document upload failed:', error);
    throw error;
  }
}
