import axios from 'axios';
import { Chat } from '@/types/chats';
import { MessageListResponse, SendMessageResponse } from '@/types/messages';
import { API_ENDPOINTS, getApiUrl } from '@/constants/api';

export interface ChatsListResponse {
  items: Chat[];
  total: number;
  page: number;
  size: number;
  pages: number;
}

export async function fetchChats({
  page = 1,
  size = 20,
  token,
}: {
  page?: number;
  size?: number;
  token?: string | null;
}): Promise<ChatsListResponse> {
  const res = await axios.get<ChatsListResponse>(getApiUrl(`${API_ENDPOINTS.CHATS}/`), {
    params: {
      page,
      size,
    },
    headers: {
      Authorization: `Bearer ${token}`,
      Accept: 'application/json',
    },
  });
  if (!res.data) {
    throw new Error('Failed to fetch chat data');
  }
  return res.data;
}

export async function fetchMessages({
  chatId,
  page = 1,
  size = 30,
  token,
}: {
  chatId: number;
  page?: number;
  size?: number;
  token?: string | null;
}): Promise<MessageListResponse> {
  const res = await axios.get<MessageListResponse>(
    getApiUrl(`${API_ENDPOINTS.CHATS}/${chatId}/messages/`),
    {
      params: {
        page,
        size,
      },
      headers: {
        Authorization: `Bearer ${token}`,
        Accept: 'application/json',
      },
    }
  );
  if (!res.data) {
    throw new Error('Failed to fetch messages');
  }
  return res.data;
}

export interface MessageContent {
  content: string;
  type: 'text';
  additionalData: {
    device: string;
    location: string;
  };
}

export interface SendMessageParams {
  chatId?: number;
  message: MessageContent;
  attachments?: Array<{ documentId: number }>;
  token?: string | null;
  onResponse: (response: SendMessageResponse) => void;
  onError?: (error: unknown) => void;
}

function isSendMessageResponse(obj: unknown): obj is SendMessageResponse {
  const casted = obj as SendMessageResponse;
  return (
    casted &&
    casted.chatId !== undefined &&
    casted.systemMessageId !== undefined &&
    casted.userMessageId !== undefined &&
    Array.isArray(casted.attachments) &&
    casted.message?.content !== undefined &&
    casted.message?.type !== undefined
  );
}

export async function sendMessage({
  chatId,
  message,
  attachments,
  token,
  onResponse,
  onError,
  onStreamStart,
  onStreamEnd,
}: SendMessageParams & {
  onStreamStart?: () => void;
  onStreamEnd?: () => void;
}): Promise<void> {
  const payload = {
    chatId,
    message,
    attachments,
  };

  const response = await fetch(getApiUrl(`/messages/stream`), {
    method: 'POST',
    headers: {
      Authorization: `Bearer ${token}`,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(payload),
  });

  if (!response.body) {
    throw new Error('No response body found');
  }

  // Signal that streaming has started
  onStreamStart?.();

  const stream = response.body.pipeThrough(new TextDecoderStream());
  const reader = stream.getReader();
  let buffer: Partial<SendMessageResponse> = { attachments: [] };

  try {
    while (true) {
      const { value, done } = await reader.read();
      if (done) break;
      const lines = value.split('\n');
      for (const line of lines) {
        const match = line.match(/^data:\s*(.*)$/);
        if (match) {
          try {
            const parsed = JSON.parse(match[1]);
            switch (parsed?.type) {
              case 'stream_send_message_response':
                buffer = { ...buffer, ...parsed.data };
                break;
              case 'final_data':
                buffer = { ...buffer, additionalData: parsed.data };
                break;
              case 'content': {
                const message = buffer.message ?? {
                  content: '',
                  type: 'text',
                };
                message.content = message.content + parsed.data;
                buffer = {
                  ...buffer,
                  message,
                };
                break;
              }
            }
            if (isSendMessageResponse(buffer)) {
              onResponse(buffer);
            } else {
              console.log(`Waiting for message to be ready`);
            }
          } catch (e) {
            console.error('Failed to parse line from stream:', line, e);
            onError && onError(e);
          }
        }
        else if (line.trim().length > 0 && line.trim() !== 'event: end') {
          console.error('Could not parse line from stream', line);
          onError && onError(new Error('Could not parse line from stream: ' + line));
        }
      }
    }
  } finally {
    // Signal that streaming has ended
    onStreamEnd?.();
  }
}
