import { create } from 'zustand';
import { fetchChats, sendMessage } from '@/api/chats';
import { Chat } from '@/types/chats';
import { Attachment, Message, MessageTypeValue, SendMessageResponse } from '@/types/messages';

interface OptimisticMessage {
  content: string;
  attachments?: Attachment[];
}

interface ChatState {
  chats: Chat[];
  isLoading: boolean;
  isSubmitting: boolean;
  hasMore: boolean;
  currentPage: number;
  optimisticMessage: OptimisticMessage | null;
  jobSummaryConfirmed: boolean;
  isRetryButtonShown: boolean;
  isMessagesSpacingActive: boolean;
  fetchChats: (token: string) => Promise<void>;
  loadMoreChats: (token: string) => Promise<void>;
  addChat: (newChat: Chat) => void;
  sendMessage: (
    chatId: number | undefined,
    content: string,
    token: string,
    attachments?: Attachment[],
    onChatId?: (chatId: number) => void
  ) => Promise<void>;
  appendMessages: (messages: Message[], chatId: number) => void;
  replaceMessages: (messages: Message[], chatId: number) => void;
  setJobSummaryConfirmed: (confirmed: boolean) => void;
  setIsMessagesSpacingActive: (active: boolean) => void;
}

function mergeMessages(
  current: Message[],
  systemResponse: SendMessageResponse,
  userMessage: { content: string; attachments: Attachment[] }
): Message[] {
  return [
    {
      content: systemResponse.message.content,
      attachments: systemResponse.attachments,
      id: systemResponse.systemMessageId,
      senderType: 'system',
      timestamp: new Date().toISOString(),
      type: MessageTypeValue.Text,
      additionalData: systemResponse.additionalData,
    },
    {
      id: systemResponse.userMessageId,
      content: userMessage.content,
      attachments: userMessage.attachments,
      type: MessageTypeValue.Text,
      timestamp: new Date().toISOString(),
      senderType: 'user',
    },
    ...current.filter(
      (m) => m.id !== systemResponse.systemMessageId && m.id !== systemResponse.userMessageId
    ),
  ];
}

export const useChats = create<ChatState>((set, get) => ({
  chats: [],
  isLoading: false,
  isSubmitting: false,
  hasMore: true,
  currentPage: 1,
  optimisticMessage: null,
  jobSummaryConfirmed: false,
  isRetryButtonShown: false,
  isMessagesSpacingActive: false,

  setJobSummaryConfirmed: (confirmed: boolean) => {
    set({ jobSummaryConfirmed: confirmed });
  },

  fetchChats: async (token: string) => {
    set({ isLoading: true });
    try {
      const response = await fetchChats({ token });
      set({
        chats: response.items,
        hasMore: response.pages > 1,
        currentPage: 1,
      });
    } catch (error) {
      console.error('Failed to fetch chats:', error);
      set({ chats: [] });
    } finally {
      set({ isLoading: false });
    }
  },

  loadMoreChats: async (token: string) => {
    const { currentPage, hasMore, isLoading } = get();
    if (!hasMore || isLoading) return;

    set({ isLoading: true });
    try {
      const response = await fetchChats({
        token,
        page: currentPage + 1,
      });

      set((state) => ({
        chats: [...state.chats, ...response.items],
        hasMore: response.page < response.pages,
        currentPage: currentPage + 1,
      }));
    } catch (error) {
      console.error('Failed to load more chats:', error);
    } finally {
      set({ isLoading: false });
    }
  },

  addChat: (newChat: Chat) => {
    set((state) => ({
      chats: [...state.chats, newChat],
    }));
  },

  appendMessages: (messages, chatId) => {
    set((state) => ({
      chats: state.chats.map((chat) =>
        chat.id === chatId
          ? {
              ...chat,
              messages: [...(chat.messages || []), ...messages],
            }
          : chat
      ),
    }));
  },

  replaceMessages: (messages, chatId) => {
    set((state) => ({
      chats: state.chats.some((chat) => chat.id === chatId)
        ? state.chats.map((chat) =>
            chat.id === chatId
              ? {
                  ...chat,
                  messages,
                }
              : chat
          )
        : [
            ...state.chats,
            {
              id: chatId,
              messages,
              title: '',
              status: '',
            },
          ],
    }));
  },

  sendMessage: async (
    chatId: number | undefined,
    content: string,
    token: string,
    attachments: Attachment[] = [],
    onChatId?: (chatId: number) => void
  ) => {
    chatId && onChatId && onChatId(chatId);
    set({
      isSubmitting: true,
      isRetryButtonShown: false,
    });
    try {
      set({
        optimisticMessage: {
          content,
          attachments,
        },
        isMessagesSpacingActive: true,
      });

      await sendMessage({
        chatId,
        message: {
          content,
          type: 'text',
          additionalData: {
            device: navigator.userAgent,
            location: window.location.href,
          },
        },
        attachments: attachments.map((attachment) => ({
          documentId: attachment.documentId,
        })),
        token,
        onError: () => {
          set(() => ({ isRetryButtonShown: true }));
        },
        onResponse: (res) => {
          onChatId && onChatId(res.chatId);
          set((state) => ({
            isSubmitting: false,
            chats: state.chats.some((c) => c.id === res.chatId)
              ? state.chats.map((c) =>
                  c.id === res.chatId
                    ? {
                        ...c,
                        messages: mergeMessages(c.messages || [], res, {
                          content,
                          attachments,
                        }),
                      }
                    : c
                )
              : [
                  ...state.chats,
                  {
                    id: res.chatId,
                    messages: mergeMessages([], res, {
                      content,
                      attachments,
                    }),
                    title: '',
                    status: '',
                  },
                ],
          }));
        },
      });

      if (!chatId) {
        const chat = (
          await fetchChats({
            token,
            page: 1,
            size: 1,
          })
        ).items[0];
        const chatExists = get().chats.some((c) => c.id === chat.id);

        if (!chatExists) {
          set((state) => ({
            chats: [chat, ...state.chats],
          }));
        }
      }
    } catch (error) {
      console.error('Failed to send message:', error);
      set({ isRetryButtonShown: true });
    }
  },

  setIsMessagesSpacingActive: (isMessagesSpacingActive: boolean) => {
    set({ isMessagesSpacingActive });
  },
}));
