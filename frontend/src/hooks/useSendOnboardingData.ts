import axios from 'axios';
import { useAuth } from '@clerk/nextjs';
import { getApiUrl, API_ENDPOINTS } from '@/constants/api';
import { AddressFields } from '@/components/MultiFieldInput/MultiFieldInput.types';

interface OnboardingData {
  selectedAddress: string | AddressFields;
  selectedRole: string | null;
  selectedPropertyType: string | null;
  selectedOwnershipType: string | null;
}

interface DemoRequestData {
  companyName: string;
  businessEmail: string;
  numberOfPropertiesManaged: string;
}

export const useSendOnboardingData = () => {
  const { getToken } = useAuth();

  const sendOnboardingData = async ({
    selectedAddress,
    selectedPropertyType,
    selectedOwnershipType,
  }: OnboardingData) => {
    const token = await getToken();

    let requestData;

    if (typeof selectedAddress === 'string') {
      requestData = {
        idealPostcodesAddressId: selectedAddress,
        type: selectedPropertyType === null ? 'house' : selectedPropertyType.toLowerCase(),
        tenureType:
          selectedOwnershipType === null ? 'freehold' : selectedOwnershipType.toLowerCase(),
      };
    } else {
      requestData = {
        manualAddress: {
          streetLine1: selectedAddress.line1 || '',
          streetLine2: selectedAddress.line2 || '',
          townOrCity: selectedAddress.city || '',
          postcode: selectedAddress.postcode || '',
        },
        type: selectedPropertyType === null ? 'house' : selectedPropertyType.toLowerCase(),
        tenureType:
          selectedOwnershipType === null ? 'freehold' : selectedOwnershipType.toLowerCase(),
      };
    }

    const addressSendRequest = axios.post(getApiUrl(`${API_ENDPOINTS.PROPERTIES}/`), requestData, {
      headers: {
        Authorization: `Bearer ${token}`,
        Accept: 'application/json',
        'Content-Type': 'application/json',
      },
    });

    return {
      addressSendRequest,
    };
  };

  const sendDemoRequest = async ({
    companyName,
    businessEmail,
    numberOfPropertiesManaged,
  }: DemoRequestData) => {
    const token = await getToken();

    const demoSendRequest = axios.post(
      getApiUrl('user/request-b2b-demo/'),
      {
        companyName,
        businessEmail,
        numberOfPropertiesManaged,
      },
      {
        headers: {
          Authorization: `Bearer ${token}`,
          Accept: 'application/json',
          'Content-Type': 'application/json',
        },
      }
    );

    return {
      demoSendRequest,
    };
  };

  return { sendOnboardingData, sendDemoRequest };
};
