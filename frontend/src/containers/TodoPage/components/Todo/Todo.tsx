import { Typography } from '@/components/Typography/Typography';
import { format } from 'date-fns';
import { FC } from 'react';
import styles from './Todo.module.scss';
import { TodoCheckbox } from './TodoCheckbox';
import { HugeiconsIcon } from '@/components/HugeiconsIcon';
import * as StrokeStandard from '@hugeicons-pro/core-stroke-standard';
import { IconSvgObject } from '@hugeicons/react';
import { cn } from '@/lib/utils';
import { ITodoDto } from '@/api/todos/types';

interface ITodoProps extends ITodoDto {
  onChecked?: () => void;
  onClick?: () => void;
}

export const Todo: FC<ITodoProps> = ({
  name,
  description,
  doneDate,
  dueDate,
  onChecked = () => {},
  onClick = () => {},
}) => (
  <div className="flex justify-between items-center pt-4 pb-4 border-b border-gray-200 hover:bg-gray-50">
    <div className="flex gap-2 items-start w-full">
      <div className="mb-1">
        <TodoCheckbox checked={!!doneDate} onChange={onChecked} />
      </div>
      <div className="flex flex-col w-full gap-2">
        <p
          className={cn(
            'ellipsis text-base leading-[18px] text-gray-900 font-[Quasimoda]',
            !!doneDate && 'line-through'
          )}
        >
          {name}
        </p>
        {description && (
          <p
            className={cn(
              'ellipsis text-xs text-gray-600 font-[Quasimoda] max-w-[200px]',
              !!doneDate && 'line-through'
            )}
          >
            {description}
          </p>
        )}
      </div>
    </div>
    <div className={styles.dueDateContainer}>
      {dueDate && (
        <Typography variant="body-s" font="quasimoda" className={styles.dueDate}>
          Due: {format(dueDate, 'dd/MM/yy')}
        </Typography>
      )}
      <HugeiconsIcon
        icon={StrokeStandard.ArrowRight01Icon as unknown as IconSvgObject}
        size={20}
        className={styles.arrowIcon}
        color="var(--color-gray-600)"
        onClick={onClick}
      />
    </div>
  </div>
);
