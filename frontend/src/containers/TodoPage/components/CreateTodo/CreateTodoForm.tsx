import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { DatePicker } from '@/components/DatePicker';
import { toNaiveISOString } from '@/lib/toNaiveISOString';
import { createTodo } from '@/api/todos/todos';
import { API_ENDPOINTS, getApiUrl } from '@/constants/api';
import useSWRMutation from 'swr/mutation';
import { useAuth } from '@clerk/nextjs';
import { withZodSchema } from 'formik-validator-zod';
import { useFormik } from 'formik';
import { ICreateTodoDto } from '@/api/todos/types';
import { z } from 'zod';
import { Button } from '@/components/ui/button';
import { HugeiconsIcon } from '@/components/HugeiconsIcon';
import { IconSvgObject } from '@hugeicons/react';
import * as SolidStandard from '@hugeicons-pro/core-solid-standard';
import { mutate } from 'swr';
import { toast } from '@/components/ui/sonner';

const validationSchema = z.object({
  name: z.string().min(1, 'Name should have minimum 1 letter'),
  description: z.string().nullable(),
  dueDate: z.string().nullable(),
  doneDate: z.string().nullable(),
});

interface CreateTodoFormProps {
  onSubmit?: () => void;
}

export const CreateTodoForm = ({ onSubmit }: CreateTodoFormProps) => {
  const { getToken } = useAuth();
  const { trigger } = useSWRMutation(`${getApiUrl(API_ENDPOINTS.TODOS)}/`, createTodo);

  const formik = useFormik<ICreateTodoDto>({
    initialValues: {
      name: '',
      description: '',
      dueDate: null,
      doneDate: null,
    },
    validate: withZodSchema(validationSchema),
    onSubmit: async (values) => {
      const token = await getToken();

      (document.activeElement as HTMLElement)?.blur();

      onSubmit?.();

      if (!token) {
        console.error('No token available for creating todo');
        return;
      }

      trigger({
        token,
        createTodoDto: {
          ...values,
          dueDate: toNaiveISOString(values.dueDate),
          doneDate: toNaiveISOString(values.doneDate),
        },
      });
      mutate(`${getApiUrl(API_ENDPOINTS.TODOS)}/`);

      toast.success({ title: 'New to-do created!' });
    },
  });

  return (
    <form autoComplete="off" onSubmit={formik.handleSubmit}>
      <div className="flex flex-col gap-6 mb-4">
        <div className="grid gap-3">
          <Label htmlFor="name">What do you want to get done</Label>
          <Input
            id="name"
            placeholder="e.g. Powerwash patio"
            name="name"
            value={formik.values.name}
            onChange={(value) => formik.setFieldValue('name', value.target.value)}
          />
        </div>
        <div className="grid gap-3">
          <Label htmlFor="dueDate">Set a due date</Label>
          <DatePicker
            value={formik.values.dueDate ? new Date(formik.values.dueDate) : undefined}
            onChange={(value: Date | null) => {
              formik.setFieldValue('dueDate', value ? value.toISOString() : null);
            }}
          />
        </div>
        <div className="grid gap-3">
          <Label htmlFor="description">Description</Label>
          <div className="grid gap-1">
            <Textarea
              id="description"
              placeholder="Placeholder text"
              rows={4}
              name="description"
              value={formik.values.description ?? ''}
              onChange={(value) => formik.setFieldValue('description', value.target.value)}
            />
            <p className="text-muted-foreground text-sm">300 characters</p>
          </div>
        </div>
      </div>
      <Button type="submit" size="lg" className="w-full" disabled={!formik.values.name}>
        <HugeiconsIcon icon={SolidStandard.Add01Icon as unknown as IconSvgObject} /> Add a new to-do
      </Button>
    </form>
  );
};
