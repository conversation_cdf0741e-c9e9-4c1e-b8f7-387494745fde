import { ITodoDto } from '@/api/todos/types';
import { DatePicker } from '@/components/DatePicker';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { useFormik } from 'formik';
import z from 'zod';
import { withZodSchema } from 'formik-validator-zod';
import { useAuth } from '@clerk/nextjs';
import useSWRMutation from 'swr/mutation';
import { API_ENDPOINTS, getApiUrl } from '@/constants/api';
import { updateTodo } from '@/api/todos/todos';
import { toNaiveISOString } from '@/lib/toNaiveISOString';
import { mutate } from 'swr';
import { useTodoContext } from '../../useTodoContext';
import { useEffect } from 'react';

const validationSchema = z.object({
  name: z.string().min(1, 'Name should have minimum 1 letter'),
  description: z.string().nullable(),
  dueDate: z.string().nullable(),
  doneDate: z.string().nullable(),
});

interface EditTodoDetailsProps {
  todo: ITodoDto;
  onClick?: () => void;
}

export const EditTodoDetails: React.FC<EditTodoDetailsProps> = ({ todo, onClick }) => {
  const { getToken } = useAuth();
  const { setIsEditFormDirty } = useTodoContext();
  const { trigger } = useSWRMutation(
    todo.id ? getApiUrl(`${API_ENDPOINTS.TODOS}/${todo.id}`) : null,
    updateTodo
  );

  const formik = useFormik({
    initialValues: {
      name: todo.name,
      description: todo.description ?? null,
      dueDate: todo.dueDate ?? null,
      doneDate: todo.doneDate ?? null,
    },
    validate: withZodSchema(validationSchema),
    onSubmit: async (values) => {
      const token = await getToken();

      (document.activeElement as HTMLElement)?.blur();

      if (!token) {
        console.error('No token available for creating todo');
        return;
      }

      onClick?.();

      await trigger({
        token,
        updateTodo: {
          ...values,
          dueDate: toNaiveISOString(values.dueDate),
          doneDate: toNaiveISOString(values.doneDate),
        },
      });
      mutate(`${getApiUrl(API_ENDPOINTS.TODOS)}/`);
      mutate(getApiUrl(`${API_ENDPOINTS.TODOS}/${todo.id}/`));
    },
  });

  useEffect(() => setIsEditFormDirty(formik.dirty), [setIsEditFormDirty, formik.dirty]);

  return (
    <form autoComplete="off" onSubmit={formik.handleSubmit}>
      <div className="flex flex-col gap-6 mb-4">
        <div className="grid gap-3">
          <Label htmlFor="name">Edit to-do title</Label>
          <Input
            id="name"
            placeholder="e.g. Powerwash patio"
            name="name"
            value={formik.values.name}
            onChange={(value) => formik.setFieldValue('name', value.target.value)}
          />
        </div>
        <div className="grid gap-3">
          <Label htmlFor="dueDate">Edit due date</Label>
          <DatePicker
            value={formik.values.dueDate ? new Date(formik.values.dueDate) : undefined}
            onChange={(value: Date | null) => {
              formik.setFieldValue('dueDate', value ? value.toISOString() : null);
            }}
          />
        </div>
        <div className="grid gap-3">
          <Label htmlFor="description">Edit description</Label>
          <div className="grid gap-1">
            <Textarea
              id="description"
              placeholder="Placeholder text"
              rows={4}
              name="description"
              value={formik.values.description ?? ''}
              onChange={(value) => formik.setFieldValue('description', value.target.value)}
            />
            <p className="text-muted-foreground text-sm">300 characters</p>
          </div>
        </div>
      </div>
      <Button type="submit" size="lg" className="w-full md:w-auto!" disabled={!formik.values.name}>
        Save
      </Button>
    </form>
  );
};
