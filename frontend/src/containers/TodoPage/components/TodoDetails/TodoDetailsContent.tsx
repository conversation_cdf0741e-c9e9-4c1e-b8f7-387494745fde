'use client';

import { ITodoDto } from '@/api/todos/types';
import { Button } from '@/components/ui/button';
import { HugeiconsIcon } from '@/components/HugeiconsIcon';
import { IconSvgObject } from '@hugeicons/react';
import * as StrokeStandard from '@hugeicons-pro/core-stroke-standard';
import Image from 'next/image';
import { Spinner } from '@/components/Spinner';
import { format } from 'date-fns';
// import { Composer } from '@/components/Composer';
import { google } from 'calendar-link';

interface ITodoDetailsContentProps {
  todo?: ITodoDto | null;
}

export const TodoDetailsContent = ({ todo }: ITodoDetailsContentProps) => {
  if (!todo) {
    return (
      <div className="w-full h-full flex items-center justify-center">
        <Spinner color="var(--colors-yellow-500)" size={100} />
      </div>
    );
  }

  const description = `
  ${todo.description ?? ''}
  
  📲 View in the Hey Alfie app: 
  ${window && window.location.href}
  `;

  const googleCalendarLink = google({
    title: todo.name,
    description,
    start: new Date(),
    allDay: true,
  });

  return (
    <div className="w-full overflow-x-hidden pb-10">
      <div className="flex flex-col gap-3 mb-4 justify-start pt-5 items-start">
        <p className="text-lg font-[Quasimoda] leading-[27px]">{todo.name}</p>
        {todo.dueDate && (
          <p className="text-sm font-[Quasimoda] leading-[21px] text-gray-600 flex items-center gap-1">
            <HugeiconsIcon
              icon={StrokeStandard.Calendar04Icon as unknown as IconSvgObject}
              size={17}
            />
            Due by {format(todo.dueDate, 'dd/MM/yyyy')}
          </p>
        )}
        <Button
          size="xs"
          variant="ghost"
          className="shrink-0 text-gray-600"
          onClick={() => window.open(googleCalendarLink, '_blank')}
        >
          <Image src="google-calendar.svg" alt="Google Calendar Icon" width={20} height={20} />
          Add to Google Calendar
        </Button>
      </div>
      <p className="text-gray-600 text-sm leading-[21px] font-[Quasimoda]">Description</p>
      {todo.description && (
        <p className="text-sm font-[Quasimoda] leading-[21px] text-gray-800 mb-2">
          {todo.description}
        </p>
      )}
      {/* TODO: in the next iteration we will add the ability to chat
      <div className="w-full absolute bottom-0 left-0 right-0 p-4 bg-white shadow--[0px_-3px_8.1px_0px_rgba(20,27,52,0.08)] rounded-xl">
        <p className="font-[Quasimoda] text-xl leading-[30px] font-bold mb-3">
          What can I help you with?
        </p>
        <Composer />
      </div> */}
    </div>
  );
};
