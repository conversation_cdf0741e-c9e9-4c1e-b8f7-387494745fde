import { motion, AnimatePresence } from 'framer-motion';
import { Typography } from '@/components/Typography/Typography';
import { Todo } from '../Todo';
import { ITodoDto } from '@/api/todos/types';

interface ITodosListProps {
  todos: ITodoDto[];
  renderCTAButton?: () => React.ReactNode;
  onChecked: (id: ITodoDto['id']) => void;
  onClick: (id: ITodoDto['id']) => void;
}

export const TodoList = ({ todos, renderCTAButton, onClick, onChecked }: ITodosListProps) => (
  <div>
    <div className="flex items-center justify-between mb-4">
      <Typography font="quasimoda" variant="h4">
        To-Do List
      </Typography>
      {renderCTAButton?.()}
    </div>
    <div className="flex flex-col">
      <AnimatePresence>
        {todos.map((todo) => (
          <motion.div
            key={todo.id}
            initial={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0, margin: 0, padding: 0 }}
            transition={{ duration: 0.3 }}
          >
            <Todo
              {...todo}
              name={todo.name}
              description={todo.description}
              doneDate={todo.doneDate}
              dueDate={todo.dueDate}
              onChecked={() => onChecked(todo.id)}
              onClick={() => onClick(todo.id)}
            />
          </motion.div>
        ))}
      </AnimatePresence>
    </div>
  </div>
);
