import { useQueryState, parseAsInteger } from 'nuqs';
import { ITodoDto } from '@/api/todos/types';
import { useGetTodos, useUpdateTodo } from '@/hooks/useTodoService';
import { toNaiveISOString } from '@/lib/toNaiveISOString';
import { useMemo, useState } from 'react';
import { mutate } from 'swr';
import { API_ENDPOINTS, getApiUrl } from '@/constants/api';
import { useTodoContext } from './useTodoContext';
import { toast } from '@/components/ui/sonner';

export const useTodoPage = () => {
  const [removingIds, setRemovingIds] = useState<number[]>([]);
  const [selectedTodoId, setSelectedTodoId] = useQueryState<ITodoDto['id']>('todo', parseAsInteger);
  const { data: todos = [] } = useGetTodos();
  const { trigger: markTodoAsDone } = useUpdateTodo();
  const { confirmAction } = useTodoContext();

  const handleChecked = async (id: ITodoDto['id']) => {
    try {
      await confirmAction('markAsDone', null);
      // do nothing, the user cancelled the action
    } catch (_err) {
      const url = `${getApiUrl(API_ENDPOINTS.TODOS)}/`;
      const optimisticDoneDate = toNaiveISOString(new Date().toISOString());

      mutate(
        url,
        async (currentTodos: ITodoDto[] = []) => {
          const todo = currentTodos.find((todo) => todo.id === id);

          if (!todo) {
            return;
          }

          await markTodoAsDone({
            todoId: id,
            data: { ...todo, doneDate: optimisticDoneDate },
          });

          return currentTodos.map((todo) =>
            todo.id === id ? { ...todo, doneDate: optimisticDoneDate } : todo
          );
        },
        {
          optimisticData: todos.map((todo) =>
            todo.id === id ? { ...todo, doneDate: optimisticDoneDate } : todo
          ),
          rollbackOnError: true,
          populateCache: true,
          revalidate: false,
        }
      );

      setRemovingIds((prev) => [...prev, id]);
      setTimeout(() => {
        setRemovingIds((prev) => prev.filter((removingId) => removingId !== id));
        toast.success({ title: `${todos.find((todo) => todo.id === id)?.name} done` });
      }, 800);
    }
  };

  const handleCloseTodoDetails = () => {
    setSelectedTodoId(null);
  };

  const handleClickTodo = (id: number) => {
    setSelectedTodoId(id);
  };

  const filteredTodos = useMemo(
    () => todos.filter((todo) => !todo.doneDate || removingIds.includes(todo.id)),
    [todos, removingIds]
  );

  return {
    todos: filteredTodos,
    removingIds,
    selectedTodoId,
    markTodoAsDone,
    handleChecked,
    handleCloseTodoDetails,
    handleClickTodo,
  };
};
