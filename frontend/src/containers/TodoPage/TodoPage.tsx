'use client';

import { TodoList } from './components/TodoList/TodoList';
import { CreateTodo } from './components/CreateTodo';
import { TodoDetails } from './components/TodoDetails';
import { useTodoPage } from './useTodoPage';
import { TodoConfirmDialog } from './components/TodoConfirmDialog';

export const TodoPage = () => {
  const { todos, selectedTodoId, handleChecked, handleClickTodo, handleCloseTodoDetails } =
    useTodoPage();

  return (
    <div className="py-6 w-full mx-auto px-4 overflow-x-auto max-w-[798px]">
      <TodoList
        todos={todos}
        renderCTAButton={() => <CreateTodo />}
        onChecked={handleChecked}
        onClick={handleClickTodo}
      />
      <TodoDetails
        isOpen={selectedTodoId !== null}
        onClose={handleCloseTodoDetails}
        todoId={selectedTodoId}
      />
      <TodoConfirmDialog />
    </div>
  );
};
