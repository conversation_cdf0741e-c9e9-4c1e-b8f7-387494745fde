'use client';

import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';

import { useAuth } from '@clerk/nextjs';

import { PropertyHeader } from '@/components/PropertyHeader';
import { PropertyHeaderState } from '@/components/PropertyHeader/PropertyHeader.types';
import { Button } from '@/components/Button';
import { ButtonColor, ButtonType } from '@/components/Button/Button.types';
import { FileUploadGrid } from '@/components/FileUploadManager';
import { HugeiconsIcon } from '@/components/HugeiconsIcon';

import { useWidgets } from '@/hooks/useWidgets';
import { usePropertyDetailsActions } from '@/hooks/usePropertyDetails';
import { mapUserPropertyRelationTypeToDisplayText } from '@/api/properties';
import { AddressType } from '@/components/AddressRoleForm/AddressRoleForm.types';
import { useFiles } from '@/containers/FilesPage/useFiles';
import { isCategorizedFile, UploadedFile } from '@/types/file';
import { filter, groupBy, lowerCase, upperFirst } from 'lodash';

import { IconSvgObject } from '@hugeicons/react';
import * as StrokeStandard from '@hugeicons-pro/core-stroke-standard';

import styles from './PropertyProfilePage.module.scss';
import { NotificationAlert } from '@/components/NotificationAlert/NotificationAlert';

interface CircularCountButtonProps {
  count: number;
}

const CircularCountButton: React.FC<CircularCountButtonProps> = ({ count }) => (
  <div style={{ display: count === 0 ? 'none' : 'flex' }}>
    <Button
      type={ButtonType.PRIMARY}
      color={ButtonColor.GREEN_PRIMARY}
      className={styles.circularCountButton}
    >
      {count}
    </Button>
  </div>
);

export const PropertyProfilePage: React.FC = () => {
  const { getToken } = useAuth();
  const { properties, fetchProperties } = useWidgets();
  const { createProperty } = usePropertyDetailsActions();

  const [propertyHeaderState, setPropertyHeaderState] = useState<PropertyHeaderState>(
    PropertyHeaderState.EMPTY
  );
  const [address, setAddress] = useState<string>('');
  const [ownerStatus, setOwnerStatus] = useState<string | undefined>(undefined);
  const [profileImageUrl, setProfileImageUrl] = useState<string>('');

  const fileInputRef = useRef<HTMLInputElement>(null);

  const refreshPropertyData = useCallback(async () => {
    const token = await getToken();
    if (token) {
      await fetchProperties(token);
    }
  }, [getToken, fetchProperties]);

  const {
    uploadFiles,
    uploadingFiles,
    firstWarningNotification,
    firstInfoNotification,
    firstErrorNotification,
    deleteNotification,
    files,
  } = useFiles({
    setUploadContext: 'propertyProfile',
    getUploadContext: null,
    onChanged: refreshPropertyData,
  });

  const filesSortedByCategory = useMemo(() => {
    return groupBy(
      filter(files, (file) => isCategorizedFile(file)),
      (file) => {
        const fileWithCategory = file as UploadedFile & { category?: string };
        return fileWithCategory.category
          ? upperFirst(lowerCase(fileWithCategory.category))
          : 'Other';
      }
    );
  }, [files]);

  const getCategoryCount = useCallback(
    (categoryName: string) => {
      switch (categoryName.toLowerCase()) {
        case 'property details':
          return filesSortedByCategory['Property details']?.length || 0;
        case 'appliances':
          // Handle both "Appliance" (singular from API) and "Appliances" (plural)
          return (
            (filesSortedByCategory['Appliance']?.length || 0) +
            (filesSortedByCategory['Appliances']?.length || 0)
          );
        case 'files':
          // For files, count all categorized files plus any "Other" category
          const totalFiles = Object.values(filesSortedByCategory).flat().length;
          return totalFiles;
        default:
          return 0;
      }
    },
    [filesSortedByCategory]
  );

  useEffect(() => {
    if (properties.length > 0) {
      const currentProperty = properties[0];
      const fullAddress = `${currentProperty.address.streetLine1}, ${currentProperty.address.townOrCity}, ${currentProperty.address.postcode}`;

      setAddress(fullAddress);
      setPropertyHeaderState(PropertyHeaderState.FILLED);

      if (currentProperty.userPropertyRelationshipType) {
        const ownerStatusText = mapUserPropertyRelationTypeToDisplayText(
          currentProperty.userPropertyRelationshipType
        );
        setOwnerStatus(ownerStatusText);
      }
    }
  }, [properties]);

  const handleUploadDocument = () => {
    fileInputRef.current?.click();
  };

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFiles = event.target.files;
    if (!selectedFiles || selectedFiles.length === 0) return;

    const fileArray = Array.from(selectedFiles);
    void uploadFiles(fileArray);

    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const handleProfileImageChange = (file: File) => {
    const imageUrl = URL.createObjectURL(file);
    setProfileImageUrl(imageUrl);
  };

  const handleAddressComplete = async (
    data:
      | { address: string; displayAddress: string; role: string }
      | { address: AddressType; role: string }
  ) => {
    try {
      if (properties.length === 0) {
        await createProperty({
          address: data.address,
          role: data.role,
        });

        const token = await getToken();
        if (token) {
          await fetchProperties(token);
        }
      }

      let displayAddress: string;
      if ('displayAddress' in data) {
        displayAddress = data.displayAddress;
      } else if (typeof data.address === 'string') {
        displayAddress = data.address;
      } else {
        displayAddress =
          `${data.address.line1}, ${data.address.city}, ${data.address.postcode}`.trim();
      }

      setAddress(displayAddress);

      let ownerStatusText = '';
      switch (data.role) {
        case 'owner':
          ownerStatusText = 'Owner and occupier';
          break;
        case 'landlord':
          ownerStatusText = 'Landlord';
          break;
        case 'tenant':
          ownerStatusText = 'Tenant';
          break;
        default:
          ownerStatusText = data.role;
      }

      setOwnerStatus(ownerStatusText);
      setPropertyHeaderState(PropertyHeaderState.FILLED);
    } catch (error) {
      console.error('Failed to save address:', error);
    }
  };

  return (
    <div className={styles.container}>
      <h1 className={styles.heading}>Property Profile</h1>

      <div className={styles.content}>
        <PropertyHeader
          address={address}
          ownerStatus={ownerStatus}
          profileImageUrl={profileImageUrl}
          state={propertyHeaderState}
          onProfileImageChange={handleProfileImageChange}
          onAddressComplete={handleAddressComplete}
        />
      </div>

      <div className={styles.content}>
        <h2 className={styles.subheading}>Build your Property Profile</h2>

        <div className={styles.uploadSection}>
          <div className={styles.uploadContent}>
            <div className={styles.houseIcon}>
              <img src="/House_8x.png" alt="House illustration" className={styles.houseImage} />
            </div>
            <div className={styles.uploadText}>
              <h3 className={styles.uploadTitle}>
                Upload documents for the quickest way to complete your Property Profile
              </h3>
              <p className={styles.uploadDescription}>
                Alfie can automatically complete your Property Profile based on your floor plan,
                survey report, insurance policy, utility bills, appliance receipts, and other
                documents. Upload files or sync with Google Drive to supercharge Alfie&apos;s
                personalised assistance to you
              </p>
              <Button
                type={ButtonType.PRIMARY}
                color={ButtonColor.GREEN_PRIMARY}
                onClick={handleUploadDocument}
              >
                Upload documents
              </Button>
            </div>
          </div>
        </div>

        <input
          type="file"
          ref={fileInputRef}
          onChange={handleFileChange}
          accept=".pdf,.png,.jpg,.jpeg,.webp,.gif,.heic,.heif"
          multiple
          style={{ display: 'none' }}
        />

        {uploadingFiles.length > 0 && (
          <div className={styles.uploadingFiles}>
            <FileUploadGrid files={uploadingFiles} showRemoveButton={false} />
          </div>
        )}

        <div className={styles.alerts}>
          {firstErrorNotification && (
            <NotificationAlert notification={firstErrorNotification} onClose={deleteNotification} />
          )}
          {firstWarningNotification && (
            <NotificationAlert
              notification={firstWarningNotification}
              onClose={deleteNotification}
            />
          )}
          {firstInfoNotification && (
            <NotificationAlert notification={firstInfoNotification} onClose={deleteNotification} />
          )}
        </div>

        <div className={styles.navigationCards}>
          <a href="/property-profile/property-details" className={styles.navigationCard}>
            <div className={styles.cardContent}>
              <h3 className={styles.cardTitle}>Property Details</h3>
              <p className={styles.cardDescription}>
                Basic details including type, size, bedrooms, and bathrooms
              </p>
            </div>
            <div className={styles.cardIcon}>
              <CircularCountButton count={getCategoryCount('property details')} />
              <HugeiconsIcon
                icon={StrokeStandard.ArrowRight01Icon as unknown as IconSvgObject}
                size={20}
              />
            </div>
          </a>

          <a href="/property-profile/appliances" className={styles.navigationCard}>
            <div className={styles.cardContent}>
              <h3 className={styles.cardTitle}>Appliances</h3>
              <p className={styles.cardDescription}>
                Heating, washing machine, kitchen, and other appliances
              </p>
            </div>
            <div className={styles.cardIcon}>
              <CircularCountButton count={getCategoryCount('appliances')} />
              <HugeiconsIcon
                icon={StrokeStandard.ArrowRight01Icon as unknown as IconSvgObject}
                size={20}
              />
            </div>
          </a>

          <a href="/property-profile/files" className={styles.navigationCard}>
            <div className={styles.cardContent}>
              <h3 className={styles.cardTitle}>Files</h3>
              <p className={styles.cardDescription}>Index of all uploaded files</p>
            </div>
            <div className={styles.cardIcon}>
              <CircularCountButton count={getCategoryCount('files')} />
              <HugeiconsIcon
                icon={StrokeStandard.ArrowRight01Icon as unknown as IconSvgObject}
                size={20}
              />
            </div>
          </a>
        </div>
      </div>
    </div>
  );
};
