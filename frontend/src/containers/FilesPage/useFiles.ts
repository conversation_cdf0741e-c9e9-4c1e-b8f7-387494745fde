import { ErrorDocumentsNotification, IrrelevantDocumentsNotification } from './types';
import {
  DocumentDto,
  fetchDocuments,
  markDocumentStatusDisplayed,
  UploadContext,
  uploadDocument,
} from '@/api/documents';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { getFileType } from '@/utils/fileUtils';
import { getUploadErrorMessage } from './utils/getUploadErrorMessage';
import { useAuth } from '@clerk/nextjs';
import { UniversalFile, UploadedFile } from '@/types/file';

interface UseAppliancesDocumentUploadOptions {
  uploadContext: UploadContext;
  pollingInterval?: number;
  onLoaded?: () => void;
  onChanged?: () => void;
}

const SUPPORTED_TYPES = [
  'application/pdf',
  'image/jpeg',
  'image/jpg',
  'image/png',
  'image/webp',
  'image/gif',
  'image/heic',
  'image/heif',
];

const SUPPORTED_EXTENSIONS = ['.pdf', '.png', '.jpg', '.jpeg', '.webp', '.gif', '.heic', '.heif'];

function getUniversalFileId(document: DocumentDto) {
  return `document-${document.id}`;
}

export const useFiles = (options: UseAppliancesDocumentUploadOptions) => {
  const { getToken } = useAuth();

  const [files, setFiles] = useState<UniversalFile[]>([]);
  const [unsupportedFiles, setUnsupportedFiles] = useState<string[]>([]);

  const [irrelevantDocumentsNotification, setIrrelevantDocumentsNotification] =
    useState<IrrelevantDocumentsNotification | null>(null);
  const [errorDocumentsNotification, setErrorDocumentsNotification] =
    useState<ErrorDocumentsNotification | null>(null);

  const [isUploading, setIsUploading] = useState(false);
  const pollingIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const recentlyUploadedFilesRef = useRef<Set<string>>(new Set());
  const notifiedSavedFilesRef = useRef<Set<string>>(new Set());
  const notifiedErrorFilesRef = useRef<Set<number>>(new Set());
  const notifiedIrrelevantFilesRef = useRef<Set<number>>(new Set());

  const { pollingInterval = 5000, onLoaded, uploadContext, onChanged } = options;

  const filesHash = useMemo(() => {
    const sorted = files.sort((a, b) => a.id.localeCompare(b.id));
    return JSON.stringify(sorted);
  }, [files]);

  useEffect(() => {
    onChanged?.();
  }, [filesHash, onChanged]);

  const validateFiles = useCallback((files: File[]) => {
    const supported: File[] = [];
    const unsupported: string[] = [];

    files.forEach((file) => {
      const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase();
      const isSupported =
        SUPPORTED_TYPES.includes(file.type) || SUPPORTED_EXTENSIONS.includes(fileExtension);

      if (!isSupported) {
        unsupported.push(file.name);
      } else {
        supported.push(file);
      }
    });

    return { supported, unsupported };
  }, []);

  const pollDocuments = useCallback(async () => {
    const token = await getToken();
    if (!token) return;

    const documents = await fetchDocuments(token, uploadContext);

    const savedDocsNotDisplayed = documents.filter(
      (doc) =>
        doc.status === 'saved' &&
        !doc.hasStatusBeenDisplayed &&
        recentlyUploadedFilesRef.current.has(doc.fileName) &&
        !notifiedSavedFilesRef.current.has(doc.fileName)
    );
    const errorDocs = documents.filter(
      (doc) =>
        doc.status === 'error' &&
        !doc.hasStatusBeenDisplayed &&
        !notifiedErrorFilesRef.current.has(doc.id)
    );

    const irrelevantDocs = documents.filter(
      (doc) =>
        doc.status === 'irrelevant' &&
        !doc.hasStatusBeenDisplayed &&
        !notifiedIrrelevantFilesRef.current.has(doc.id)
    );

    if (savedDocsNotDisplayed.length > 0) {
      const uniqueFiles = Array.from(
        new Map(savedDocsNotDisplayed.map((doc) => [doc.fileName, doc])).values()
      );

      uniqueFiles.forEach((doc) => {
        notifiedSavedFilesRef.current.add(doc.fileName);
      });
    }

    if (errorDocs.length > 0) {
      const fileList = errorDocs.map((doc) => `'${doc.fileName}'`).join(', ');
      const notification: ErrorDocumentsNotification = {
        id: 'error-documents',
        type: 'error',
        title: 'Upload failed',
        message: `Failed to process ${fileList}. Please try again.`,
        documentCount: errorDocs.length,
        documents: errorDocs,
      };
      setErrorDocumentsNotification(notification);

      errorDocs.forEach((doc) => {
        notifiedErrorFilesRef.current.add(doc.id);
      });
    }

    if (irrelevantDocs.length > 0) {
      const fileList = irrelevantDocs.map((doc) => `'${doc.fileName}'`).join(', ');
      const notification: IrrelevantDocumentsNotification = {
        id: 'irrelevant-documents',
        type: 'warning',
        title: 'No information saved to your Property Profile',
        message: `Alfie could not find any relevant property information from ${fileList} and has not saved ${irrelevantDocs.length > 1 ? 'the files' : 'the file'} on your Property Profile.`,
        documentCount: irrelevantDocs.length,
        documents: irrelevantDocs,
      };
      setIrrelevantDocumentsNotification(notification);

      irrelevantDocs.forEach((doc) => {
        notifiedIrrelevantFilesRef.current.add(doc.id);
      });
    }

    const toFileStatus = (status: DocumentDto['status']): UploadedFile['status'] => {
      switch (status) {
        case 'processing':
        case 'saved':
          return 'uploading';
        case 'processingCompleted':
        case 'info':
          return 'success';
        case 'irrelevant':
        case 'error':
        case 'warning':
          return 'error';
      }
    };

    const fetchedFiles = documents.map(
      (document): UniversalFile => ({
        id: getUniversalFileId(document),
        documentId: document.id,
        name: document.fileName,
        type:
          document.browserMimeType.includes('pdf') ||
          document.fileName.toLowerCase().endsWith('.pdf')
            ? 'pdf'
            : 'image',
        size: document.sizeInKiloBytes,
        status: toFileStatus(document.status),
        createdAt: document.createdAt,
        category: document.category ?? undefined,
        label: document.label ?? undefined,
      })
    );

    setFiles((prevFiles) => {
      const uniqueFilesMap = new Map<string, UniversalFile>();
      prevFiles.forEach((file) => {
        uniqueFilesMap.set(file.id, file);
      });
      fetchedFiles.forEach((file) => {
        uniqueFilesMap.set(file.id, file);
      });
      return Array.from(uniqueFilesMap.values());
    });
    onLoaded?.();
  }, [getToken, onLoaded, uploadContext]);

  useEffect(() => {
    if (pollingIntervalRef.current) {
      clearInterval(pollingIntervalRef.current);
    }

    pollingIntervalRef.current = setInterval(pollDocuments, pollingInterval);
    void pollDocuments();

    return () => {
      if (pollingIntervalRef.current) {
        clearInterval(pollingIntervalRef.current);
        pollingIntervalRef.current = null;
      }
    };
  }, [pollDocuments, pollingInterval]);

  const uploadFiles = useCallback(
    async (files: File[]) => {
      const { supported, unsupported } = validateFiles(files);

      setIrrelevantDocumentsNotification(null);
      setErrorDocumentsNotification(null);
      setUnsupportedFiles([]);
      recentlyUploadedFilesRef.current.clear();
      notifiedSavedFilesRef.current.clear();
      notifiedErrorFilesRef.current.clear();
      notifiedIrrelevantFilesRef.current.clear();

      if (unsupported.length > 0) {
        setUnsupportedFiles((prev) => [...prev, ...unsupported]);
      }

      if (supported.length === 0) return;

      setIsUploading(true);

      const token = await getToken();
      if (!token) {
        setIsUploading(false);
        return;
      }

      supported.forEach((file) => {
        recentlyUploadedFilesRef.current.add(file.name);
      });

      const uploadPromises = supported.map(async (file) => {
        const tempId = `upload-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
        const uploadingFile: UniversalFile = {
          id: tempId,
          name: file.name,
          type: getFileType(file),
          size: file.size,
          file,
          createdAt: new Date().toISOString(),
          status: 'uploading',
        };

        setFiles((prev) => [uploadingFile, ...prev]);

        try {
          const uploadedFile = await uploadDocument(file, token, uploadContext);

          setFiles((prev) =>
            prev.map((f) =>
              f.id === tempId
                ? {
                    ...f,
                    id: getUniversalFileId(uploadedFile),
                    documentId: uploadedFile.id,
                  }
                : f
            )
          );
        } catch (error) {
          console.error('Upload failed for file:', file.name, error);
          const errorMessage = getUploadErrorMessage(error, file.name);
          const uploadErrorNotification = {
            id: 'upload-error-documents',
            type: 'error' as const,
            title: 'Upload failed',
            message: errorMessage,
            documentCount: 1,
            documents: [],
          };

          setErrorDocumentsNotification(uploadErrorNotification);
          setFiles((prev) => prev.filter(f => f.id !== tempId));
        }
      });

      await Promise.all(uploadPromises);
      setIsUploading(false);
    },
    [getToken, uploadContext, validateFiles]
  );

  const dismissIrrelevantDocumentsNotification = useCallback(async () => {
    setIrrelevantDocumentsNotification(null);
    notifiedIrrelevantFilesRef.current.clear();

    const token = await getToken();
    if (token) {
      try {
        await markDocumentStatusDisplayed(token, 'appliancesPage');
      } catch (error) {
        console.error('Failed to mark irrelevant documents status as displayed:', error);
      }
    }
  }, [getToken]);

  const dismissErrorDocumentsNotification = useCallback(async () => {
    setErrorDocumentsNotification(null);
    notifiedErrorFilesRef.current.clear();

    const token = await getToken();
    if (token) {
      try {
        await markDocumentStatusDisplayed(token, 'appliancesPage');
      } catch (error) {
        console.error('Failed to mark error documents status as displayed:', error);
      }
    }
  }, [getToken]);

  const clearUnsupportedFiles = useCallback(() => {
    setUnsupportedFiles([]);
  }, []);

  const clearAllTracking = useCallback(() => {
    recentlyUploadedFilesRef.current.clear();
    notifiedSavedFilesRef.current.clear();
    notifiedErrorFilesRef.current.clear();
    notifiedIrrelevantFilesRef.current.clear();
  }, []);

  const uploadingFiles = useMemo(
    () => files.filter((file) => file.status === 'uploading'),
    [files]
  );

  return {
    uploadFiles,
    uploadingFiles,
    unsupportedFiles,
    irrelevantDocumentsNotification,
    errorDocumentsNotification,
    isUploading,
    dismissIrrelevantDocumentsNotification,
    dismissErrorDocumentsNotification,
    clearUnsupportedFiles,
    clearAllTracking,
    files,
    setFiles,
  };
};
