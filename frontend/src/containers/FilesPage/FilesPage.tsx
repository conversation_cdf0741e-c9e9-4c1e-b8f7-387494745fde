'use client';

import { filter, groupBy, lowerCase, upperFirst } from 'lodash';
import React, { useCallback, useMemo, useRef, useState } from 'react';
import { Button } from '@/components/Button';
import { ButtonType } from '@/components/Button/Button.types';
import { CategoryWithFiles } from './components/CategoryWithFiles';
import { EmptyState } from './components/EmptyState/EmptyState';
import { Spinner } from '@/components/Spinner';
import { Typography } from '@/components/Typography/Typography';
import styles from './FilesPage.module.scss';
import { useFiles } from './useFiles';
import { FileUploadGrid } from '@/components/FileUploadManager';
import { isCategorizedFile } from '@/types/file';
import { NotificationAlert } from '@/components/NotificationAlert/NotificationAlert';
import { Breadcrumbs } from '@/components/Breadcrumbs';

interface IFilesProps {}

export const FilesPage: React.FC<IFilesProps> = () => {
  const [isInitialLoading, setIsInitialLoading] = useState(true);

  const fileInputRef = useRef<HTMLInputElement | null>(null);
  const {
    uploadFiles,
    uploadingFiles,
    firstWarningNotification,
    firstErrorNotification,
    firstInfoNotification,
    deleteNotification,
    files,
  } = useFiles({
    setUploadContext: 'filesPage',
    getUploadContext: null,
    onLoaded: useCallback(() => setIsInitialLoading(false), []),
  });

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFiles = event.target.files;
    if (!selectedFiles || selectedFiles.length === 0) return;

    const fileArray = Array.from(selectedFiles);
    uploadFiles(fileArray);

    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const handleSelectFileClick = () => {
    fileInputRef.current?.click();
  };

  const filesSortedByCategory = groupBy(
    filter(files, (file) => isCategorizedFile(file)),
    (file) => (file.category ? upperFirst(lowerCase(file.category)) : 'Other')
  );

  const renderedCategoriesWithFiles = useMemo(
    () =>
      Object.entries(filesSortedByCategory).map(([category, files]) => (
        <CategoryWithFiles key={category} category={category} files={files} />
      )),
    [filesSortedByCategory]
  );

  const renderedProcessingFiles = useMemo(
    () =>
      uploadingFiles.length > 0 ? (
        <div className="mb-6">
          <Typography variant="body-s" font="quasimoda" className="mb-3 font-bold">
            Your document
          </Typography>
          {uploadingFiles.length > 0 && (
            <>
              <div className={styles.uploadingFiles}>
                <FileUploadGrid files={uploadingFiles} showRemoveButton={false} />
              </div>
            </>
          )}
        </div>
      ) : null,
    [uploadingFiles]
  );

  if (isInitialLoading) {
    return (
      <div className="w-full h-full flex items-center justify-center">
        <Spinner color="var(--colors-yellow-500)" size={100} />
      </div>
    );
  }

  return (
    <div className="py-10 w-2xl mx-auto px-4 overflow-x-auto">
      <Breadcrumbs
        path={[
          { value: 'Property Profile', url: '/property-profile' },
          { value: 'Files' },
        ]}
      />
      <Typography variant="h3" font="quasimoda" className="mb-6 font-bold text-2xl">
        Files
      </Typography>
      <input type="file" ref={fileInputRef} onChange={handleFileChange} className="hidden" />
      {files.length === 0 ? (
        <EmptyState onClick={handleSelectFileClick} />
      ) : (
        <div className="max-w-[696px] mx-auto">
          <Typography variant="h3" font="quasimoda" className="mb-6 font-bold text-base">
            Upload documents
          </Typography>
          <Typography variant="body-s" font="quasimoda" className="mb-4">
            All documents uploaded to Hey Alfie will show in your Files here. These documents
            contain information that helps Alfie provide personalised property management for you.
          </Typography>
          <Button type={ButtonType.PRIMARY} onClick={handleSelectFileClick}>
            Upload documents
          </Button>
          <div className={styles.alerts}>
            {firstErrorNotification && (
              <NotificationAlert
                notification={firstErrorNotification}
                onClose={deleteNotification}
              />
            )}
            {firstWarningNotification && (
              <NotificationAlert
                notification={firstWarningNotification}
                onClose={deleteNotification}
              />
            )}
            {firstInfoNotification && (
              <NotificationAlert
                notification={firstInfoNotification}
                onClose={deleteNotification}
              />
            )}
          </div>
          {renderedProcessingFiles}
          {renderedCategoriesWithFiles}
        </div>
      )}
    </div>
  );
};
